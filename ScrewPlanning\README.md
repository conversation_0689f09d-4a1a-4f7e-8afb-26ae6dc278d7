# ESTUN Screw Planning: Intelligent Screw Trajectory Optimization Based on CT Bone Density Analysis and Geometric Constraint Integration

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/) [![SimpleITK](https://img.shields.io/badge/SimpleITK-2.0+-green.svg)](https://simpleitk.org/) [![NumPy](https://img.shields.io/badge/NumPy-1.20+-red.svg)](https://numpy.org/) [![License](https://img.shields.io/badge/ESTUN-Algo-yellow.svg)](https://opensource.org/licenses/MIT) [![PyPI](https://img.shields.io/badge/PyPI-3775A9?logo=pypi&logoColor=white)](#python-包管理加速) [![conda](https://img.shields.io/badge/conda-44A833?logo=anaconda&logoColor=white)](#conda-包管理加速) [![arXiv](https://img.shields.io/badge/arXiv-B31B1B?logo=arxiv&logoColor=white)](#学术资源加速) [![GitLab](https://img.shields.io/badge/GitLab-FC6D26?&logo=gitlab&logoColor=white)](#gitlab) 

## ESTUN Screw Planning: 基于CT骨密度分析与几何约束融合的螺钉轨迹优化系统

![gi2LoUOGTQS2UPt_xmw8Vw](./resource/imgs/gi2LoUOGTQS2UPt_xmw8Vw.png)

## 📰 项目概述

本项目是一个基于骨密度评估和锥形空间路径积分的肩盂假体基座螺钉植入路径自动规划系统，专门用于反向肩关节置换术（Reverse Shoulder Arthroplasty, RSA）的术前规划。系统实现了以下核心功能：

- **智能路径规划**：基于15度锥形约束的候选路径生成和评估
- **多目标优化**：支持传统加权方法和Pareto最优化两种优化策略
- **精确骨密度计算**：使用验证的公式法（QCT = 17.8 + 0.7 × HU）进行骨密度转换
- **体积约束检查**：确保螺钉95%以上体积位于骨质内部
- **业务规划模式**：支持center、top、bottom三个螺钉位置的独立规划

## 🏥 项目背景

反向肩关节置换术（RSA）是治疗严重肩关节疾病的有效手术方法，特别适用于大面积不可修复的肩袖撕裂、粉碎性肱骨近端骨折等复杂病例。传统的RSA术前规划主要依赖医生经验，存在以下挑战：

- **主观性强**：规划结果高度依赖医生个人经验
- **多约束优化**：需要同时满足角度约束（15度锥形范围）、覆盖率约束（≥95%）和骨密度要求
- **复杂解剖结构**：肩胛骨形状不规则，骨质密度分布不均
- **安全性评估**：难以量化评估螺钉植入的安全性和固定强度

本系统通过智能化算法解决了这些技术挑战，提供标准化、可重复的自动规划方案。

## 🔬 技术方案

### 圆环采样技术创新

**传统方法的局限性**：
- 传统算法仅沿螺钉中心线采样，忽略了螺钉的圆柱体几何特征
- 无法准确反映螺钉与周围骨质的真实接触情况
- 可能导致骨密度评估不准确

**圆环采样方法的优势**：
1. **真实几何建模**：在每个路径采样点处生成垂直于螺钉方向的圆环
2. **多点采样**：在每个圆环上均匀分布12个采样点，全面评估周围骨质
3. **体积化评估**：通过圆环积分更准确地计算螺钉-骨质接触体积的骨密度
4. **鲁棒性验证**：要求每个圆环至少30%的点有效，整条路径至少50%的圆环有效

**数学原理**：

1. **传统方法**：中心线积分：$\int_0^L \rho(s)ds$

2. **圆环方法**：圆环平均积分：$\int_0^L \bar{\rho}(s)ds$

其中，$\bar{\rho}(s) = \frac{1}{n} \sum_{i=1}^n \rho_i(s)$， $n$ 为圆环上的采样点数（默认 $12$）



### 核心算法思路

本系统通过数学建模和几何优化实现了智能化的螺钉植入路径规划，核心思想是在满足解剖学约束的前提下，通过锥形空间搜索、圆环采样技术和多目标优化，寻找具有最佳综合性能的螺钉植入轨迹。

#### 1. 锥形空间生成算法
以螺钉起始点为锥顶，基座法向量为轴心，生成15度约束角的锥形搜索空间：
- **锥底半径**：$R_{base} = L_{screw} × tan(15°)$
- **轴心方向**：基座法向量（最理想植入方向）
- **几何建模**：采用Rodrigues旋转公式进行精确坐标变换

#### 2. 候选路径采样策略
在锥形空间内进行系统性路径枚举：
- **径向采样**：控制径向分辨率（默认30）
- **周向采样**：控制周向分辨率（默认150）
- **均匀分布**：在锥底圆形区域内生成规则分布的采样点

#### 3. 骨密度积分计算（圆环采样方法）
使用验证的公式法和圆环采样技术量化评估路径骨质强度：
- **HU转换**：$QCT = 17.8 + 0.7 × HU$
- **圆环采样**：在每个路径采样点处生成垂直于路径的圆环，在圆环上进行多点采样
- **体积积分**：$\int_0^L \bar{\rho}(s)\,ds$，其中$\bar{\rho}(s)$为圆环上各点骨密度的平均值
- **采样参数**：
  - 路径积分分辨率（默认30）：控制沿路径的采样点数量
  - 圆环采样点数（默认12）：控制每个圆环上的采样点数量

#### 4. 体积约束检查
确保螺钉95%以上体积位于骨质内：
- **圆柱体建模**：将螺钉建模为圆柱体几何
- **表面采样**：在螺钉表面进行密集采样
- **覆盖率计算**：$Coverage = N_{inside} / N_{total}$
- **安全阈值**：覆盖率≥95%才被认为是安全路径

#### 5. 多目标优化决策
支持两种优化策略：

**传统加权方法**：
- 选择骨密度积分最高的路径
- 计算速度快，适合快速规划

**Pareto最优化方法**：
- **三个目标函数**：
  - 骨密度积分（归一化）
  - 覆盖率（0-1范围）
  - 安全性评分（基于15度角度约束）
- **非支配排序**：构建Pareto前沿
- **拥挤距离**：保证解的多样性
- **多解选择**：提供多个最优解供临床选择

   

### 主要技术栈

| 技术组件 | 版本要求 | 用途说明 |
|---------|---------|---------|
| **Python** | 3.8+ | 主要开发语言 |
| **SimpleITK** | 2.0+ | 医学图像处理和坐标变换 |
| **NumPy** | 1.20+ | 数值计算和数组操作 |
| **SciPy** | 1.7+ | 科学计算和优化算法 |
| **Math** | 内置 | 数学计算和几何变换 |

### 解决方案架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据输入层    │    │   算法处理层    │    │   结果输出层    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • CT图像数据    │───▶│ • 骨密度计算    │───▶│ • 最优路径      │
│ • 肩胛骨掩膜    │    │ • 锥形空间生成  │    │ • Pareto解集    │
│ • 螺钉坐标      │    │ • 多目标优化    │    │ • JSON结果      │
│ • 螺钉规格      │    │ • 体积约束检查  │    │ • 统计信息      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ 实现细节

### 关键算法实现

#### 1. 骨密度计算模块 (`core/bone_density.py`)

**核心功能**：
- HU值到骨密度转换：`QCT = 17.8 + 0.7 × HU`
- **圆环采样骨密度积分计算**：在每个路径采样点处生成圆环，计算圆环上各点骨密度的平均值
- 体积约束检查（圆柱体表面采样）
- 掩膜内覆盖率计算

````python
def generate_ring_sampling_points(self, center_point: Point3D, direction_vector: Vector3D,
                                 radius: float, num_ring_points: int = 12) -> List[Point3D]:
    """在垂直于给定方向的平面上生成圆环采样点"""
    # 创建垂直于方向向量的两个正交向量，在圆环上均匀采样

def evaluate_path(self, path: ScrewPath, integration_resolution: int = 30,
                 ring_sampling_points: int = 12) -> ScrewPath:
    """评估螺钉路径（圆环采样方法）"""
    # 沿路径采样，在每个采样点处生成圆环，计算圆环骨密度积分
````
#### 2. 锥形空间生成模块 (`core/cone_space.py`)

**核心功能**：
- 基于15度约束角的锥形空间生成
- 候选路径均匀采样（径向×周向）
- 基座法向量计算和方向分析
- 旋转矩阵计算（Rodrigues公式）

````python
def generate_cone_space(self, apex: Point3D, axis_direction: Vector3D) -> ConeSpace:
    """生成锥形空间"""
    # 15度约束角
    base_radius = self.screw_length * math.tan(math.radians(15.0))

    cone_space = ConeSpace(
        apex=apex,
        axis=axis_direction,
        height=self.screw_length,
        base_radius=base_radius,
        constraint_angle=15.0
    )
    return cone_space
````
#### 3. 业务规划模块 (`core/business_planning.py`)

**核心功能**：
- 支持center、top、bottom三个螺钉位置
- 15度约束角默认设置
- 两种优化方法：weighted和pareto
- 自动基座法向量计算

````python
@dataclass
class BusinessPlanningInput:
    constraint_angle: float = 15.0  # 15度约束角
    optimization_method: str = "weighted"  # 优化方法
    max_pareto_solutions: int = 5  # Pareto解数量
````
#### 4. 多目标优化模块 (`core/optimization.py`)

**核心功能**：
- Pareto最优化算法（NSGA-II思想）
- 非支配排序和拥挤距离计算
- 多目标评分：骨密度、覆盖率、安全性
- 传统加权方法支持

### 数据处理流程

```mermaid
graph TD
    A[CT图像+掩膜输入] --> B[螺钉坐标加载]
    B --> C[基座法向量计算]
    C --> D[15度锥形空间生成]
    D --> E[候选路径采样]
    E --> F[骨密度积分计算]
    F --> G[体积覆盖率检查]
    G --> H{优化方法选择}
    H -->|weighted| I[加权评分选择]
    H -->|pareto| J[Pareto多目标优化]
    I --> K[最优路径输出]
    J --> L[Pareto解集输出]
```

### 核心功能模块

| 模块名称 | 文件路径 | 主要功能 |
|---------|---------|---------|
| **几何计算** | `utils/geometry.py` | Point3D、Vector3D、ScrewPath等基础几何类 |
| **图像处理** | `utils/image_processing.py` | CT图像读取、预处理和坐标变换 |
| **坐标变换** | `utils/coordinate_transform.py` | 世界坐标与图像坐标转换 |
| **方向分析** | `core/direction_analyzer.py` | 基座法向量计算和方向分析 |
| **文件IO** | `utils/io_utils.py` | 数据文件读写和格式转换 |
| **命令行接口** | `cli/business_cli.py` | 业务规划命令行工具 |

## 📁 项目结构

```
ScrewPlanning/
├── src/                           # 源代码目录
│   ├── core/                      # 核心算法模块
│   │   ├── __init__.py
│   │   ├── bone_density.py        # 骨密度计算和路径评估
│   │   ├── cone_space.py          # 锥形空间生成和候选路径采样
│   │   ├── business_planning.py   # 业务规划模块（主要入口）
│   │   ├── optimization.py        # 多目标优化算法（Pareto+加权）
│   │   └── direction_analyzer.py  # 基座方向分析
│   ├── utils/                     # 工具函数模块
│   │   ├── __init__.py
│   │   ├── geometry.py            # 3D几何计算类和函数
│   │   ├── image_processing.py    # 医学图像处理工具
│   │   ├── coordinate_transform.py # 坐标系变换工具
│   │   └── io_utils.py            # 文件输入输出工具
│   ├── cli/                       # 命令行接口
│   │   ├── __init__.py
│   │   └── business_cli.py        # 业务规划命令行工具
│   └── main.py                    # 主程序入口
├── docs/                          # 项目文档
│   ├── PROJECT_SUMMARY.md         # 项目总结
│   ├── development_progress.md    # 开发进度报告
│   ├── DELIVERY_CHECKLIST.md      # 交付清单
│   └── QUICK_START.md             # 快速开始指南
├── examples/                      # 示例和测试数据
│   ├── PlanData/                  # 规划数据示例
│   │   ├── CT.nii.gz              # 示例CT图像
│   │   ├── scapula_mask.nii.gz    # 肩胛骨掩膜
│   │   ├── screw_coords_top_bottom.json # 螺钉坐标数据
│   │   └── screw_specs.json       # 螺钉规格参数
│   └── business_planning_example.py # 业务规划示例
├── tests/                         # 测试模块
│   ├── test_pareto_optimization.py # Pareto优化测试
│   ├── test_angle_constraint.py   # 角度约束测试
│   └── diagnose_coordinates.py    # 坐标诊断工具
├── papers/                        # 参考文献
│   └── Li 等 - 2022 - Automatic surgical planning...md
├── logs/                          # 日志文件目录
├── run_business_planning.py       # 业务规划启动脚本（主要入口）
└── README.md                      # 项目说明文档
```

## ⚙️ 安装与配置

### 系统要求

- **操作系统**：Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Python版本**：3.8 或更高版本

### 依赖安装

1. **克隆项目**：
```bash
git clone <repository-url>
cd ScrewPlanning
```

2. **创建虚拟环境**（推荐）：
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

3. **安装依赖包**：
```bash
pip install SimpleITK>=2.0.0
pip install numpy>=1.20.0
pip install scipy>=1.7.0
```

### 环境验证

运行以下命令验证环境配置：
```bash
python -c "import SimpleITK as sitk; import numpy as np; import math; print('环境配置成功！')"
```

## 🚀 使用说明

### 基本使用方法

#### 1. 业务规划模式（推荐）

```python
from src.core.business_planning import BusinessPathPlanner, BusinessPlanningInput
from src.utils.io_utils import load_ct_image, load_mask_image
from src.utils.geometry import Point3D, ScrewSpec

# 加载数据
ct_image = load_ct_image("examples/PlanData/CT.nii.gz")
mask_image = load_mask_image("examples/PlanData/scapula_mask.nii.gz")

# 设置螺钉坐标
screw_coordinates = {
    "top": Point3D(69.6, -190.9, 222.5),
    "bottom": Point3D(70.4, -200.4, 208.7)
}

# 设置螺钉规格
screw_specs = {
    "top": ScrewSpec(length=20.0, radius=3.25),
    "bottom": ScrewSpec(length=20.0, radius=3.25)
}

# 创建输入参数
input_params = BusinessPlanningInput(
    ct_image=ct_image,
    mask_image=mask_image,
    screw_coordinates=screw_coordinates,
    screw_specs=screw_specs,
    constraint_angle=15.0,  # 15度约束角
    optimization_method="pareto",  # 使用Pareto优化
    max_pareto_solutions=5
)

# 执行规划
planner = BusinessPathPlanner()
result = planner.plan_screws(input_params)

# 查看结果
if result.success:
    print(f"规划成功！规划时间: {result.planning_time:.2f}秒")
    for screw_name, endpoint in result.screw_endpoints.items():
        print(f"{screw_name} 螺钉终点: ({endpoint.x:.2f}, {endpoint.y:.2f}, {endpoint.z:.2f})")
```

### 命令行使用

#### 业务规划命令行工具

```bash
# 基本用法（使用默认加权方法）
python run_business_planning.py \
    --ct "examples/PlanData/CT.nii.gz" \
    --mask "examples/PlanData/scapula_mask.nii.gz" \
    --coords "examples/PlanData/screw_coords_top_bottom.json" \
    --output "result.json"

# 使用Pareto优化方法
python run_business_planning.py \
    --ct "examples/PlanData/CT.nii.gz" \
    --mask "examples/PlanData/scapula_mask.nii.gz" \
    --coords "examples/PlanData/screw_coords_top_bottom.json" \
    --optimization-method pareto \
    --max-pareto-solutions 10 \
    --output "pareto_result.json"

# 自定义约束角度和算法参数
python run_business_planning.py \
    --ct "data/CT.nii.gz" \
    --mask "data/mask.nii.gz" \
    --coords "data/coords.json" \
    --constraint-angle 10.0 \
    --radial-resolution 40 \
    --circumferential-resolution 200 \
    --screws top bottom
```

### 示例脚本

项目提供了完整的示例脚本：

**业务规划示例**：
```bash
python examples/business_planning_example.py
```

### 数据文件格式

#### 螺钉坐标文件 (JSON格式)
```json
{
    "top": {"x": 69.6, "y": -190.9, "z": 222.5},
    "bottom": {"x": 70.4, "y": -200.4, "z": 208.7}
}
```

#### 螺钉规格文件 (JSON格式，可选)
```json
{
    "top": {"length": 20.0, "radius": 3.25},
    "bottom": {"length": 20.0, "radius": 3.25}
}
```

### 参数配置

#### 关键算法参数

| 参数名称 | 默认值 | 说明 | 取值范围 |
|---------|--------|------|---------|
| `constraint_angle` | 15.0° | 锥形约束角度 | 10-30° |
| `radial_resolution` | 30 | 径向分辨率 | 10-50 |
| `circumferential_resolution` | 150 | 周向分辨率 | 50-300 |
| `integration_resolution` | 30 | 路径积分分辨率（沿路径采样点数） | 20-50 |
| `ring_sampling_points` | 12 | 圆环采样点数（每个圆环上的采样点数） | 8-24 |
| `optimization_method` | "weighted" | 优化方法 | "weighted", "pareto" |
| `max_pareto_solutions` | 5 | Pareto解数量 | 1-20 |
| `coverage_threshold` | 0.95 | 覆盖率阈值 | 0.8-1.0 |

#### 螺钉规格参数

| 参数名称 | 默认值 | 说明 | 取值范围 |
|---------|--------|------|---------|
| `screw_length` | 20.0 mm | 螺钉长度 | 15-50 mm |
| `screw_radius` | 3.25 mm | 螺钉半径 | 2.0-5.0 mm |

## 📊 结果输出

### 优化方法对比

#### 1. 加权方法 (weighted)
- **特点**：选择骨密度积分最高的单一路径
- **优势**：计算速度快，结果确定
- **适用**：快速规划，单一最优解需求

#### 2. Pareto优化方法 (pareto)
- **特点**：提供多个非支配最优解
- **优势**：考虑多目标平衡，提供选择空间
- **适用**：复杂规划，需要多方案比较

### 规划结果展示

- 红色、蓝色的锥形范围：15度约束的候选路径范围
- 黄色直线：算法规划的最优植入路径

![image-20250730105149232](./resource/imgs/image-20250730105149232.png)



### 结果输出格式

#### JSON格式输出示例
```json
{
  "success": true,
  "planning_time": 2.34,
  "error_message": "",
  "optimization_method": "pareto",
  "screw_endpoints": {
    "top": {"x": -49.58, "y": 194.68, "z": 221.59},
    "bottom": {"x": -51.11, "y": 207.09, "z": 208.96}
  },
  "statistics": {
    "total_screws": 2,
    "successful_screws": 2,
    "constraint_angle": 15.0,
    "base_normal": {"x": 0.996, "y": 0.086, "z": 0.0}
  },
  "screw_paths": {
    "top": {
      "start_point": {"x": -69.63, "y": 190.93, "z": 222.57},
      "end_point": {"x": -49.58, "y": 194.68, "z": 221.59},
      "length": 20.0,
      "radius": 3.25,
      "bone_density_integral": 4275.39,
      "coverage_ratio": 0.952,
      "is_valid": true
    },
    "bottom": {
      "start_point": {"x": -70.45, "y": 200.42, "z": 208.73},
      "end_point": {"x": -51.11, "y": 207.09, "z": 208.96},
      "length": 20.0,
      "radius": 3.25,
      "bone_density_integral": 7225.77,
      "coverage_ratio": 0.951,
      "is_valid": true
    }
  },
  "pareto_solutions": {
    "top": [
      {
        "path": "...",
        "objectives": {
          "bone_density": 0.85,
          "coverage_ratio": 0.952,
          "safety_score": 0.92
        },
        "rank": 1,
        "crowding_distance": 1.5
      }
    ]
  }
}
```

#### 关键字段说明

**基本信息**：
- `success`: 规划是否成功
- `planning_time`: 算法计算耗时（秒）
- `optimization_method`: 使用的优化方法（"weighted"或"pareto"）
- `error_message`: 错误信息（如有）

**螺钉结果**：
- `screw_endpoints`: 各螺钉的终点坐标（世界坐标系，单位：mm）
- `screw_paths`: 详细路径信息，包括：
  - `start_point`: 起始点坐标
  - `end_point`: 终点坐标
  - `length`: 螺钉长度（mm）
  - `radius`: 螺钉半径（mm）
  - `bone_density_integral`: 骨密度积分值（数值越高表示骨质越密实）
  - `coverage_ratio`: 覆盖率（≥0.95为安全）
  - `is_valid`: 路径是否有效

**统计信息**：
- `total_screws`: 规划的螺钉总数
- `successful_screws`: 成功规划的螺钉数量
- `constraint_angle`: 使用的约束角度（度）
- `base_normal`: 基座法向量（理想植入方向）

**Pareto优化结果**（仅当使用pareto方法时）：
- `pareto_solutions`: 每个螺钉的Pareto最优解集
  - `objectives`: 三个目标函数值
    - `bone_density`: 归一化骨密度评分
    - `coverage_ratio`: 覆盖率评分
    - `safety_score`: 安全性评分（基于角度偏差）
  - `rank`: Pareto排名（1为最优前沿）
  - `crowding_distance`: 拥挤距离（保证解的多样性）




## � 系统特性

### 技术亮点

1. **15度精确约束**：基于临床实际需求的锥形约束角度
2. **圆环采样技术**：突破传统中心线采样局限，在每个路径采样点处生成垂直圆环，更真实地反映螺钉圆柱体与骨质的接触情况
3. **双优化策略**：支持快速加权方法和精确Pareto优化
4. **95%安全保障**：严格的体积覆盖率检查确保植入安全性
5. **多目标平衡**：同时优化骨密度、覆盖率和角度安全性
6. **实时计算**：高效算法设计，规划时间通常在10-20秒内

### 临床价值

- **标准化规划**：减少医生主观判断差异
- **量化评估**：提供客观的骨密度和安全性指标
- **多方案选择**：Pareto优化提供多个最优解供选择
- **风险控制**：严格的约束检查降低手术风险

## 📖 参考文献

### 主要参考论文

**Li, H., Xu, J., Zhang, D., He, Y., & Chen, X. (2022).** *Automatic surgical planning based on bone density assessment and path integral in cone space for reverse shoulder arthroplasty.* **International Journal of Computer Assisted Radiology and Surgery**, 17(8), 1535-1546.

**DOI**: [10.1007/s11548-022-02639-1](https://doi.org/10.1007/s11548-022-02639-1)

![Li 等 - 2022 - Automatic surgical planning based on bone density assessment and path integral in cone space for rev_03](./resource/imgs/Li 等 - 2022 - Automatic surgical planning based on bone density assessment and path integral in cone space for rev_03.png)