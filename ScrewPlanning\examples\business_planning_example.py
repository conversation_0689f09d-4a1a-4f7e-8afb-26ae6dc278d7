#!/usr/bin/env python3
"""
业务螺钉路径规划示例

演示如何使用新的业务规划接口进行螺钉路径规划
"""

import sys
import os
import logging

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(os.path.dirname(current_dir), 'src')
sys.path.insert(0, src_dir)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def business_planning_example():
    """业务规划示例"""
    print("=" * 60)
    print("业务螺钉路径规划示例")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from core.business_planning import BusinessPathPlanner, BusinessPlanningInput, ScrewSpec
        from utils.geometry import Point3D
        from utils.io_utils import DataLoader
        
        print("✅ 模块导入成功")
        
        # 检查数据文件是否存在
        data_dir = os.path.join(current_dir, 'PlanData')
        ct_file = os.path.join(data_dir, 'CT.nii.gz')
        mask_file = os.path.join(data_dir, 'scapula_mask.nii.gz')
        
        if not os.path.exists(ct_file):
            print(f"❌ CT文件不存在: {ct_file}")
            return False
            
        if not os.path.exists(mask_file):
            print(f"❌ 掩膜文件不存在: {mask_file}")
            return False
        
        print("✅ 数据文件检查通过")
        
        # 加载数据
        print("📂 加载数据...")
        loader = DataLoader()
        ct_image = loader.load_ct_image(ct_file)
        mask_image = loader.load_mask_image(mask_file)
        print("✅ 数据加载成功")
        
        # 定义螺钉坐标（使用LPS坐标系的基座坐标）
        print("📍 定义螺钉坐标...")
        screw_coordinates = {
            'center': Point3D(-70.0379, 195.678, 215.651),
            'top': Point3D(-69.6298, 190.934, 222.572),
            'bottom': Point3D(-70.446, 200.422, 208.731)
        }
        print(f"✅ 已定义 {len(screw_coordinates)} 个螺钉坐标（LPS坐标系基座坐标）")
        
        # 定义螺钉规格
        print("🔧 定义螺钉规格...")
        screw_specs = {
            'center': ScrewSpec(length=50.0, radius=3.25),  # 6.5mm直径，50mm长度
            'top': ScrewSpec(length=50.0, radius=3.25),
            'bottom': ScrewSpec(length=50.0, radius=3.25)
        }
        print(f"✅ 已定义 {len(screw_specs)} 个螺钉规格")
        
        # 创建输入参数
        print("⚙️ 创建规划参数...")
        input_params = BusinessPlanningInput(
            ct_image=ct_image,
            mask_image=mask_image,
            screw_coordinates=screw_coordinates,
            screw_specs=screw_specs,
            bone_threshold=200.0,
            constraint_angle=45.0,
            # 使用完整的算法参数（不简化优化算法）
            radial_resolution=30,
            circumferential_resolution=150,
            integration_resolution=30
        )
        print("✅ 规划参数创建成功")
        
        # 执行规划
        print("🚀 开始执行业务路径规划...")
        planner = BusinessPathPlanner()
        result = planner.plan_screws(input_params)
        
        # 显示结果
        print("\n" + "=" * 60)
        print("规划结果")
        print("=" * 60)
        
        if result.success:
            print("✅ 规划成功！")
            print(f"📊 规划耗时: {result.planning_time:.2f} 秒")
            print(f"🎯 成功规划螺钉数量: {len(result.screw_endpoints)}")
            
            print("\n📍 螺钉末端坐标:")
            for screw_name, endpoint in result.screw_endpoints.items():
                print(f"  {screw_name:>6}: ({endpoint.x:>8.2f}, {endpoint.y:>8.2f}, {endpoint.z:>8.2f})")
            
            if result.statistics:
                print(f"\n📈 统计信息:")
                stats = result.statistics
                print(f"  总螺钉数: {stats.get('total_screws', 'N/A')}")
                print(f"  成功螺钉数: {stats.get('successful_screws', 'N/A')}")
                
                if 'base_normal' in stats:
                    normal = stats['base_normal']
                    print(f"  基座法向量: ({normal.x:.3f}, {normal.y:.3f}, {normal.z:.3f})")
                
                if 'base_center' in stats:
                    center = stats['base_center']
                    print(f"  基座中心: ({center.x:.2f}, {center.y:.2f}, {center.z:.2f})")
            
            # 显示每条路径的详细信息
            if result.screw_paths:
                print(f"\n🔍 详细路径信息:")
                for screw_name, path in result.screw_paths.items():
                    print(f"\n  螺钉 {screw_name}:")
                    print(f"    起点: ({path.start_point.x:.2f}, {path.start_point.y:.2f}, {path.start_point.z:.2f})")
                    print(f"    终点: ({path.end_point.x:.2f}, {path.end_point.y:.2f}, {path.end_point.z:.2f})")
                    print(f"    长度: {path.length:.2f} mm")
                    print(f"    半径: {path.radius:.2f} mm")
                    print(f"    骨密度积分: {path.bone_density_integral:.2f}")
                    print(f"    覆盖率: {path.coverage_ratio:.1%}")
                    print(f"    是否有效: {'是' if path.is_valid else '否'}")
        else:
            print("❌ 规划失败")
            print(f"错误信息: {result.error_message}")
            return False
        
        # 导出结果（可选）
        try:
            output_dir = os.path.join(current_dir, 'output')
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            import json
            output_file = os.path.join(output_dir, 'business_planning_result.json')
            
            # 准备输出数据
            output_data = {
                "success": result.success,
                "planning_time": result.planning_time,
                "screw_endpoints": {},
                "statistics": result.statistics
            }
            
            # 转换螺钉末端坐标
            for screw_name, endpoint in result.screw_endpoints.items():
                output_data["screw_endpoints"][screw_name] = {
                    "x": endpoint.x,
                    "y": endpoint.y,
                    "z": endpoint.z
                }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 结果已导出到: {output_file}")
            
        except Exception as e:
            print(f"⚠️  导出结果时出错: {e}")
        
        print("\n" + "=" * 60)
        print("✅ 业务规划示例执行成功！")
        print("=" * 60)
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请确保已安装所需依赖: pip install -r ../requirements.txt")
        return False
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        logging.exception("详细错误信息:")
        return False

def main():
    """主函数"""
    setup_logging()
    
    success = business_planning_example()
    
    if success:
        print("\n🎉 恭喜！您已成功运行了业务螺钉路径规划系统")
        print("📚 您可以使用以下方式运行:")
        print("   - python run_business_planning.py --help  # 查看命令行帮助")
        print("   - python run_business_planning.py --ct CT.nii.gz --mask scapula_mask.nii.gz --coords business_screw_coords.json")
    else:
        print("\n❌ 示例执行失败，请检查错误信息并重试")
        sys.exit(1)

if __name__ == "__main__":
    main()
