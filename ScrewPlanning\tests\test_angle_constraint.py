#!/usr/bin/env python3
"""
测试15度角度约束的安全性评分计算
"""

import sys
import os
import math
import logging

# 添加src目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

from core.optimization import ParetoOptimizer, MultiObjectiveScore, ParetoSolution
from utils.geometry import ScrewPath, Point3D, Vector3D

def test_angle_constraint():
    """测试15度角度约束的安全性评分"""
    print("🧪 测试15度角度约束的安全性评分")
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建Pareto优化器（目标角度0度）
    optimizer = ParetoOptimizer(target_angle=0.0)
    
    # 参考方向：Z轴正方向（0, 0, 1）
    reference_direction = Vector3D(0, 0, 1)
    
    # 创建不同角度的测试路径
    test_cases = [
        ("0度路径", Point3D(0, 0, 0), Point3D(0, 0, 50)),      # 与参考方向一致
        ("5度路径", Point3D(0, 0, 0), Point3D(4.36, 0, 49.81)), # 约5度偏差
        ("10度路径", Point3D(0, 0, 0), Point3D(8.68, 0, 49.24)), # 约10度偏差
        ("15度路径", Point3D(0, 0, 0), Point3D(12.94, 0, 48.30)), # 约15度偏差
        ("20度路径", Point3D(0, 0, 0), Point3D(17.10, 0, 46.98)), # 约20度偏差（超出约束）
        ("30度路径", Point3D(0, 0, 0), Point3D(25.0, 0, 43.30)), # 约30度偏差（超出约束）
        ("45度路径", Point3D(0, 0, 0), Point3D(35.36, 0, 35.36)), # 约45度偏差（超出约束）
    ]
    
    test_paths = []
    for name, start, end in test_cases:
        path = ScrewPath(start, end, 3.25, 50)
        path.bone_density_integral = 1000.0  # 固定骨密度
        path.coverage_ratio = 0.96  # 固定覆盖率
        path.is_valid = True
        test_paths.append((name, path))
    
    print(f"\n📊 测试不同角度路径的安全性评分:")
    print(f"参考方向: {reference_direction}")
    print(f"目标角度: 0°")
    print(f"约束范围: ±15°")
    print("-" * 80)
    
    for name, path in test_paths:
        # 计算路径方向
        path_direction = path.get_direction_vector()
        
        # 计算与参考方向的夹角
        angle = optimizer._calculate_angle_between_vectors(path_direction, reference_direction)
        angle_degrees = math.degrees(angle)
        
        # 计算多目标评分
        scores = optimizer.calculate_multi_objective_scores([path], reference_direction)
        safety_score = scores[0].safety_score
        
        # 判断是否在约束范围内
        in_constraint = angle_degrees <= 15.0
        constraint_status = "✅ 在约束内" if in_constraint else "❌ 超出约束"
        
        print(f"{name:>10}: 角度={angle_degrees:>6.1f}°, 安全性={safety_score:>5.3f}, {constraint_status}")
    
    print("\n" + "=" * 80)
    print("📋 测试结果分析:")
    print("1. 0°路径：完美对齐，安全性 = 1.000")
    print("2. 5°-15°路径：在约束范围内，安全性线性递减")
    print("3. >15°路径：超出约束范围，安全性 = 0.000")
    print("4. 这确保了只有符合15度约束的路径才被认为是安全的")
    
    # 测试Pareto优化
    print(f"\n🎯 测试Pareto优化（只包含约束范围内的路径）:")
    valid_constraint_paths = [path for name, path in test_paths 
                             if math.degrees(optimizer._calculate_angle_between_vectors(
                                 path.get_direction_vector(), reference_direction)) <= 15.0]
    
    print(f"约束范围内的路径数量: {len(valid_constraint_paths)}")
    
    if valid_constraint_paths:
        pareto_solutions = optimizer.optimize_paths_pareto(
            valid_constraint_paths, reference_direction, max_solutions=5)
        
        print(f"Pareto最优解数量: {len(pareto_solutions)}")
        for i, solution in enumerate(pareto_solutions):
            angle = optimizer._calculate_angle_between_vectors(
                solution.path.get_direction_vector(), reference_direction)
            angle_degrees = math.degrees(angle)
            print(f"  解{i+1}: 角度={angle_degrees:.1f}°, 安全性={solution.objectives.safety_score:.3f}")
    
    print("\n✅ 15度角度约束测试完成!")

if __name__ == "__main__":
    test_angle_constraint()
