import base64
import ctypes
import hashlib
import json
import os
import shutil
import numpy as np
import tempfile
import vtk
import zipfile

from nacl.bindings import crypto_secretbox_open
from nacl.exceptions import CryptoError

key = b'ESTUN'
ScrewFileName = 'IMPLANTSCREWDATA_ImplantScrew_0'

def writeStlFile(fileName, polydata):
    if not os.path.exists(os.path.dirname(fileName)):
        os.makedirs(os.path.dirname(fileName))
    writer = vtk.vtkSTLWriter()
    writer.SetFileName(fileName)
    writer.SetInputData(polydata)
    writer.Write()

def findFirstFile(directory, fileFullName):
    filePath = ''
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file == fileFullName:
                filePath = os.path.join(root, file)
                break
    return filePath

def decrypt(ciphertext_b64: str, key_str: str, nonce_b64: str) -> str:
    # Decode Base64 strings to bytes
    ciphertext = base64.b64decode(ciphertext_b64)
    nonce = base64.b64decode(nonce_b64)
    
    # Derive 32-byte key using BLAKE2b
    key = hashlib.blake2b(
        key_str,
        digest_size=32  # crypto_secretbox_KEYBYTES (32 bytes)
    ).digest()
    
    try:
        # Decrypt using libsodium's crypto_secretbox_open
        plaintext_bytes = crypto_secretbox_open(ciphertext, nonce, key)
        return plaintext_bytes.decode('utf-8')
    except CryptoError:
        # Handle decryption failure (e.g., invalid key/ciphertext)
        return ""

def readJsonFile(filePath):
    try:
        with open(filePath, 'r', encoding='utf-8') as file:
            return json.load(file)
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

# def readJsonPolyData(filePath):
#     fixDeskJsonObj = readJsonFile(filePath)
#     nonce = fixDeskJsonObj["Nonce"]
#     ciphertext = fixDeskJsonObj["PolyData"]
#     matrixArr = fixDeskJsonObj["Matrix"]
#     fixDiskMatrixArr = fixDeskJsonObj["FixDiskMatrix"]
#     prs2IMatrixArr = fixDeskJsonObj["MatrixPrs2I"]

#     decrypted = decrypt(ciphertext, key, nonce)
#     reader = vtk.vtkXMLPolyDataReader()
#     reader.SetInputString(decrypted)
#     reader.SetReadFromInputString(True)
#     reader.Update()

#     matrix = np.array(matrixArr)
#     fixDiskMatrix = np.array(fixDiskMatrixArr)
#     prs2IMatrix = np.array(prs2IMatrixArr)

#     matrixTemp = prs2IMatrixArr @ np.linalg.inv(fixDiskMatrix) @ matrix

#     matrix = vtk.vtkMatrix4x4()
#     for i in range(4):
#         for j in range(4):
#             matrix.SetElement(i, j, matrixTemp[i][j])
            
#     print(matrix)

#     transform = vtk.vtkTransform()
#     transform.SetMatrix(matrix)
#     transform.Update()
#     transformFilter = vtk.vtkTransformPolyDataFilter()
#     transformFilter.SetInputData(reader.GetOutput())
#     transformFilter.SetTransform(transform)
#     transformFilter.Update()

#     return transformFilter.GetOutput()

def readJsonPolyData(filePath):
    fixDiskJsonObj = readJsonFile(filePath)
    nonce = fixDiskJsonObj["Nonce"]
    ciphertext = fixDiskJsonObj["PolyData"]
    matrixArr = fixDiskJsonObj["MatrixPrs2I"]

    decrypted = decrypt(ciphertext, key, nonce)
    reader = vtk.vtkXMLPolyDataReader()
    reader.SetInputString(decrypted)
    reader.SetReadFromInputString(True)
    reader.Update()

    matrix = vtk.vtkMatrix4x4()
    for i in range(4):
        for j in range(4):
            matrix.SetElement(i, j, matrixArr[i][j])

    transform = vtk.vtkTransform()
    transform.SetMatrix(matrix)
    transform.Update()
    transformFilter = vtk.vtkTransformPolyDataFilter()
    transformFilter.SetInputData(reader.GetOutput())
    transformFilter.SetTransform(transform)
    transformFilter.Update()

    return transformFilter.GetOutput()


if __name__ == "__main__":
    try:
        # 设置输入和输出路径
        inputDir = r"D:\Code\Code\bone-mineral-density-calculation\ScrewPlanning\examples\PlanData\case02"
        outputDir = r"D:\Code\Code\bone-mineral-density-calculation\ScrewPlanning\examples\PlanData\case02"
        inputFileName = "PLANPROSTHESISDATA_ScapulaPlan_fixDisk.json"
        outputFileName = "PLANPROSTHESISDATA_ScapulaPlan_fixDisk.stl"

        # 构建完整的文件路径
        inputFilePath = f'{inputDir}\\{inputFileName}'
        outputFilePath = f'{outputDir}\\{outputFileName}'

        # 确保输出目录存在
        import os
        if not os.path.exists(outputDir):
            os.makedirs(outputDir)

        # 读取JSON并转换为STL
        screwPolyData = readJsonPolyData(inputFilePath)
        writeStlFile(outputFilePath, screwPolyData)
        print(f"已成功导出: {outputFilePath}")

    except Exception as e:
        print(f"Error: {e}")