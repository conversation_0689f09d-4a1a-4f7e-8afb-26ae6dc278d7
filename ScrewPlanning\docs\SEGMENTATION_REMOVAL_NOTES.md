# 分割功能移除说明

## 修改概述

根据用户需求，已将螺钉规划系统中的自动分割功能移除，因为用户将直接提供已分割好的肩胛骨掩膜。

## 主要修改内容

### 1. 图像处理模块 (`src/utils/image_processing.py`)

**移除的功能：**
- `apply_threshold()` - 阈值分割函数
- `extract_largest_connected_component()` - 连通分量提取函数
- `create_bone_surface_model()` - 从CT图像自动创建骨骼表面模型

**新增的功能：**
- `validate_mask_image()` - 验证掩膜图像有效性
- `clean_mask_image()` - 清理掩膜图像（可选后处理）
- `create_bone_surface_model_from_mask()` - 从掩膜直接创建骨骼表面模型

### 2. 默认参数更新 (`src/utils/io_utils.py`)

**螺钉参数更新：**
- 螺钉长度：38.97mm → 50.0mm
- 螺钉半径：1.65mm → 3.25mm（对应6.5mm直径）

### 3. 用户界面更新 (`src/ui/main_window.py`)

**界面改进：**
- 更新掩膜加载对话框标题和提示
- 添加掩膜验证功能
- 增加用户提示信息，说明需要提供已分割好的掩膜

### 4. 新增真实数据示例 (`examples/real_data_planning_example.py`)

**新示例功能：**
- 加载真实CT数据和肩胛骨掩膜
- 解析螺钉坐标JSON文件
- 自动生成参考点
- 使用更新的螺钉参数进行规划

### 5. 文档更新 (`README.md`)

**文档改进：**
- 更新技术参数说明
- 添加数据准备要求
- 明确说明需要提供已分割好的掩膜
- 更新使用流程

## 数据要求

### 输入文件

1. **CT图像** (`CT.nii.gz`)
   - 格式：NIfTI (.nii.gz)
   - 内容：原始CT扫描数据

2. **肩胛骨掩膜** (`scapula_mask.nii.gz`)
   - 格式：NIfTI (.nii.gz)
   - 内容：已分割好的肩胛骨区域
   - 要求：二值图像，肩胛骨区域为1，背景为0

3. **螺钉坐标** (`screw_coordinates.json`) - 可选
   - 格式：JSON
   - 内容：螺钉入点坐标信息

### 数据质量要求

- CT图像和掩膜必须具有相同的空间分辨率和坐标系
- 掩膜必须包含有效的前景区域
- 坐标系应为世界坐标系（物理坐标）

## 使用方法

### 1. 图形界面使用

```bash
python run_screw_planning.py
```

1. 加载CT图像
2. 加载肩胛骨掩膜
3. 选择参考点
4. 执行规划

### 2. 真实数据示例

```bash
python examples/real_data_planning_example.py
```

此示例将自动：
- 加载 `examples/PlanData/` 目录下的数据
- 解析螺钉坐标
- 生成参考点
- 执行规划并导出结果

## 技术说明

### 分割功能替代方案

由于移除了自动分割功能，建议使用以下工具进行肩胛骨分割：

1. **3D Slicer** - 免费开源医学图像分析软件
2. **ITK-SNAP** - 专业的医学图像分割工具
3. **MITK** - 医学图像处理工具包
4. **深度学习分割模型** - 如nnU-Net等

### 掩膜质量检查

系统会自动验证掩膜质量：
- 检查是否包含前景区域
- 验证数据类型和格式
- 可选的形态学清理操作

### 螺钉参数

更新后的螺钉参数：
- 直径：6.5mm
- 长度：50mm
- 这些参数已在所有相关模块中更新

## 兼容性说明

- 保留了原有的API接口，但标记为已弃用
- 旧的分割相关函数会抛出 `NotImplementedError`
- 建议使用新的 `create_bone_surface_model_from_mask()` 方法

## 注意事项

1. **数据质量**：确保提供的掩膜质量良好，边界清晰
2. **坐标系统**：确保CT图像和掩膜使用相同的坐标系
3. **文件格式**：仅支持NIfTI格式的图像文件
4. **内存使用**：大尺寸图像可能需要较多内存

## 后续开发建议

1. **质量控制**：添加更多的掩膜质量检查功能
2. **格式支持**：考虑支持更多图像格式
3. **自动化**：开发批处理功能处理多个病例
4. **验证工具**：添加掩膜与CT图像配准验证功能
