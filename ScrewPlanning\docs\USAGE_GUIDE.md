# 螺钉规划系统使用指南

## 系统概述

本系统是基于骨密度评估和锥形空间路径积分的肩盂假体基座螺钉植入路径规划系统。

**重要更新：**
- ✅ 已移除自动分割功能
- ✅ 螺钉参数已更新为6.5mm×50mm规格
- ✅ 直接使用提供的肩胛骨分割掩膜

## 数据准备

### 必需文件

1. **CT图像** (`CT.nii.gz`)
   - 原始CT扫描数据
   - NIfTI格式

2. **肩胛骨掩膜** (`scapula_mask.nii.gz`)
   - 已分割好的肩胛骨区域
   - 二值图像（肩胛骨=1，背景=0）
   - 与CT图像具有相同的空间分辨率

3. **螺钉坐标** (`screw_coordinates.json`) - 可选
   - 包含螺钉入点坐标信息
   - JSON格式

### 数据质量要求

- CT图像和掩膜必须配准良好
- 掩膜边界清晰，无明显噪声
- 坐标系为世界坐标系（物理坐标）

## 使用方法

### 方法1：编程接口（推荐）

```bash
# 查看系统信息和使用说明
python run_screw_planning.py
```

**注意：图形用户界面已被移除，请使用编程接口**

### 方法2：真实数据示例

```bash
# 运行真实数据示例
python examples/real_data_planning_example.py
```

此示例将：
- 自动加载 `examples/PlanData/` 目录下的数据
- 解析螺钉坐标并生成参考点
- 执行规划并导出结果到 `examples/output/`

### 方法3：编程接口详细示例

```python
import sys
sys.path.append('src')

from utils.io_utils import DataLoader
from core.business_planning import BusinessPathPlanner, BusinessPlanningInput, ScrewSpec
from utils.geometry import Point3D

# 加载数据
loader = DataLoader()
ct_image = loader.load_ct_image('examples/PlanData/CT.nii.gz')
mask_image = loader.load_mask_image('examples/PlanData/scapula_mask.nii.gz')

# 定义参考点（示例坐标，需要根据实际数据调整）
reference_points = [
    Point3D(-45.2, -12.8, 15.6),  # P1: 假体基座中心
    Point3D(-42.1, -15.3, 18.2),  # P2: 平面参考点1
    Point3D(-48.7, -10.1, 12.9),  # P3: 平面参考点2
    Point3D(-44.8, -8.5, 20.1),   # P4: 方向参考点
]

# 创建规划器
planner = PathPlanner(
    ct_image=ct_image,
    mask_image=mask_image,  # 肩胛骨掩膜（重要！）
    screw_length=50.0,     # 50mm长度
    screw_radius=3.25,     # 6.5mm直径
)

# 执行规划
result = planner.plan_screw_paths(reference_points)

# 查看结果
if result.success:
    print(f"规划成功！找到 {len(result.optimal_paths)} 条最优路径")
    for i, path in enumerate(result.optimal_paths):
        print(f"螺钉 {i+1}:")
        print(f"  起点: {path.start_point}")
        print(f"  终点: {path.end_point}")
        print(f"  骨密度积分: {path.bone_density_integral:.2f}")
        print(f"  路径长度: {path.length:.2f} mm")
else:
    print(f"规划失败: {result.error_message}")

# 导出结果
from utils.io_utils import ResultExporter
exporter = ResultExporter()
exporter.export_planning_result(result, 'output/planning_result.json')
print("结果已导出到 output/planning_result.json")
```

## 参数说明

### 螺钉参数（已更新）

- **长度**：50.0mm
- **直径**：6.5mm（半径3.25mm）

### 规划参数

- **约束角度**：45°（可调整）
- **径向分辨率**：30（影响计算精度）
- **周向分辨率**：150（影响计算精度）
- **积分分辨率**：30（影响骨密度计算精度）
- **骨骼阈值**：200 HU

### 性能参数

- **快速模式**：径向分辨率=20，周向分辨率=60
- **标准模式**：径向分辨率=30，周向分辨率=150
- **高精度模式**：径向分辨率=40，周向分辨率=200

## 结果解读

### 规划成功指标

- **成功率** > 5%：表示找到了有效路径
- **骨密度积分**：数值越大表示路径质量越好
- **安全评分**：评估路径的安全性

### 输出文件

1. **规划结果** (`*_planning_result.json`)
   - 包含所有路径信息和统计数据

2. **规划报告** (`*_planning_report.md`)
   - 人类可读的规划结果报告

3. **路径数据** (`*_paths.csv`) - 可选
   - 详细的路径坐标和评分数据

## 故障排除

### 常见问题

**Q1: 掩膜加载失败**
```
解决方案：
1. 检查掩膜文件格式是否为NIfTI
2. 确保掩膜包含前景区域（非全零）
3. 验证掩膜与CT图像的空间匹配
```

**Q2: 规划失败，无有效路径**
```
解决方案：
1. 检查参考点选择是否合理
2. 降低约束角度（如从45°降到30°）
3. 调整骨骼阈值参数
4. 检查掩膜质量
```

**Q3: 计算时间过长**
```
解决方案：
1. 降低分辨率参数
2. 使用快速模式参数
3. 检查系统内存是否充足
```

**Q4: 坐标系不匹配**
```
解决方案：
1. 确保CT和掩膜使用相同的坐标系
2. 检查图像的origin和spacing信息
3. 必要时重新配准数据
```

### 调试模式

```bash
# 启用详细日志
python -c "import logging; logging.basicConfig(level=logging.DEBUG)"
python examples/real_data_planning_example.py
```

## 最佳实践

1. **数据质量**：使用高质量的分割掩膜
2. **参考点选择**：确保参考点位于合理的解剖位置
3. **参数调整**：根据具体病例调整规划参数
4. **结果验证**：仔细检查规划结果的合理性
5. **备份数据**：保存原始数据和规划结果

## 技术支持

如遇到问题，请检查：
1. 系统日志文件（`logs/` 目录）
2. 数据文件完整性
3. 依赖库版本兼容性
4. 系统资源使用情况

## 版本信息

- **当前版本**：1.0.0（分割功能移除版）
- **螺钉规格**：6.5mm×50mm
- **支持格式**：NIfTI (.nii, .nii.gz)
- **Python版本**：3.8+
