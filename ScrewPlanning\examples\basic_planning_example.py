#!/usr/bin/env python3
"""
基本螺钉路径规划示例

演示如何使用编程接口进行螺钉路径规划
"""

import sys
import os
import logging

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(os.path.dirname(current_dir), 'src')
sys.path.insert(0, src_dir)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def basic_planning_example():
    """基本规划示例"""
    print("=" * 60)
    print("基本螺钉路径规划示例")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from utils.io_utils import DataLoader
        from core.path_planning import PathPlanner
        from utils.geometry import Point3D
        
        print("✅ 模块导入成功")
        
        # 检查数据文件是否存在
        data_dir = os.path.join(current_dir, 'PlanData')
        ct_file = os.path.join(data_dir, 'CT.nii.gz')
        mask_file = os.path.join(data_dir, 'scapula_mask.nii.gz')
        
        if not os.path.exists(ct_file):
            print(f"❌ CT文件不存在: {ct_file}")
            print("请确保examples/PlanData/目录下有CT.nii.gz文件")
            return False
            
        if not os.path.exists(mask_file):
            print(f"❌ 掩膜文件不存在: {mask_file}")
            print("请确保examples/PlanData/目录下有scapula_mask.nii.gz文件")
            return False
        
        print("✅ 数据文件检查通过")
        
        # 加载数据
        print("📂 加载数据...")
        loader = DataLoader()
        ct_image = loader.load_ct_image(ct_file)
        mask_image = loader.load_mask_image(mask_file)
        print("✅ 数据加载成功")
        
        # 定义参考点（示例坐标）
        print("📍 定义参考点...")
        reference_points = [
            Point3D(-45.2, -12.8, 15.6),  # P1: 假体基座中心
            Point3D(-42.1, -15.3, 18.2),  # P2: 平面参考点1
            Point3D(-48.7, -10.1, 12.9),  # P3: 平面参考点2
            Point3D(-44.8, -8.5, 20.1),   # P4: 方向参考点
        ]
        print(f"✅ 已定义 {len(reference_points)} 个参考点")
        
        # 创建规划器
        print("🔧 创建路径规划器...")
        planner = PathPlanner(
            ct_image=ct_image,
            mask_image=mask_image,
            screw_length=50.0,     # 50mm长度
            screw_radius=3.25,     # 6.5mm直径
            # 使用较低的分辨率以加快演示速度
            radial_resolution=15,
            circumferential_resolution=75,
            integration_resolution=15
        )
        print("✅ 规划器创建成功")
        
        # 执行规划
        print("🚀 开始执行路径规划...")
        result = planner.plan_screw_paths(reference_points)
        
        # 显示结果
        print("\n" + "=" * 60)
        print("规划结果")
        print("=" * 60)
        
        if result.success:
            print("✅ 规划成功！")
            print(f"📊 规划耗时: {result.planning_time:.2f} 秒")
            print(f"🎯 最优路径数量: {len(result.optimal_paths)}")
            
            if result.statistics:
                stats = result.statistics
                print(f"📈 候选路径总数: {stats.get('total_candidates', 'N/A')}")
                print(f"✅ 有效路径数量: {stats.get('valid_candidates', 'N/A')}")
                if stats.get('total_candidates', 0) > 0:
                    success_rate = (stats.get('valid_candidates', 0) / stats.get('total_candidates', 1)) * 100
                    print(f"📊 成功率: {success_rate:.1f}%")
            
            # 显示每条路径的详细信息
            for i, path in enumerate(result.optimal_paths):
                print(f"\n螺钉 {i+1}:")
                print(f"  起点: ({path.start_point.x:.1f}, {path.start_point.y:.1f}, {path.start_point.z:.1f})")
                print(f"  终点: ({path.end_point.x:.1f}, {path.end_point.y:.1f}, {path.end_point.z:.1f})")
                print(f"  骨密度积分: {path.bone_density_integral:.2f}")
                print(f"  路径长度: {path.length:.2f} mm")
                print(f"  是否有效: {'是' if path.is_valid else '否'}")
        else:
            print("❌ 规划失败")
            print(f"错误信息: {result.error_message}")
            return False
        
        # 导出结果（可选）
        try:
            output_dir = os.path.join(current_dir, 'output')
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            from utils.io_utils import ResultExporter
            exporter = ResultExporter()
            
            # 导出JSON格式
            json_file = os.path.join(output_dir, 'basic_planning_result.json')
            exporter.export_planning_result(result, json_file)
            print(f"\n💾 结果已导出到: {json_file}")
            
        except Exception as e:
            print(f"⚠️  导出结果时出错: {e}")
        
        print("\n" + "=" * 60)
        print("✅ 示例执行成功！")
        print("=" * 60)
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("请确保已安装所需依赖: pip install -r ../requirements.txt")
        return False
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        logging.exception("详细错误信息:")
        return False

def main():
    """主函数"""
    setup_logging()
    
    success = basic_planning_example()
    
    if success:
        print("\n🎉 恭喜！您已成功运行了螺钉路径规划系统")
        print("📚 更多信息请查看:")
        print("   - README.md - 项目概述")
        print("   - docs/user_manual.md - 用户手册")
        print("   - docs/algorithm_design.md - 算法设计")
    else:
        print("\n❌ 示例执行失败，请检查错误信息并重试")
        sys.exit(1)

if __name__ == "__main__":
    main()
