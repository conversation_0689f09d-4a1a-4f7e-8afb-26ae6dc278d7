# Automatic surgical planning based on bone density assessment and path integral in cone space for reverse shoulder arthroplasty

Haitao Li1 . <PERSON><PERSON> $\mathsf { x u } ^ { 1 } \oplus$ . <PERSON><PERSON><PERSON><PERSON>' $\cdot \cdot$ Yaohua ${ \mathsf { H e } } ^ { 2 , 4 }$ . <PERSON><PERSON> Chen13

Received: 16 December 2021 / Accepted: 31 March 2022 / Published online: 30 April 2022   
$\circledcirc$ CARS 2022

# Abstract

Purpose Reverse shoulder arthroplasty (RSA) is an effective surgery for severe shoulder joint diseases. Traditionall, the preoperative planning procedure of RSA is manuall conducted by experienced surgeons, resulting in prolonged operating time and unreliable drilling paths of the prosthetic fixation screws. In this study, an automatic surgical planning algorithm for RSA was proposed to compute the optimal path of screw implantation.

Methods Firstly, a cone-shaped space containing altenative paths for each screw is generated using geometric parameters. Then, the volume constraint is applied to automatically remove inappropriate paths outside the bone boundary. Subsequently, the integral of grayscale value of the CT is used to evaluate the bone density and tocompute the optimal solution. An automatic surgical planning software for RSA was also developed with the aforementioned algorithms.

Results Twenty-four clinical cases were used for preoperative planning to evaluate the accuracy and efficiency of the system. Results demonstrated that the angles among the prosthetic fixation screws were all within constraint angle $( 4 5 ^ { \circ } )$ , and the stability rate of the planned prosthesis was $9 4 . 9 2 \%$ . The average time for the automatic planning algorithm was $4 . 3 9 \mathrm { s }$ , and $8 3 . 9 6 \mathrm { ~ s ~ }$ for the whole procedure. Repetitive experiments were also conducted to demonstrate the robustness of our system, and the variance of the stability coefficient was $0 . 0 2 7 \%$ .

Conclusions In contrast to the cumbersome manual planning of the existing methods for RSA, our method requires only simple interaction operations. It enables efficient and precise automatic preoperative planning to simulate the ideal placement of the long prosthetic screws for the long-term stability of the prosthesis. In the future, it will have great clinical application prospects in RSA.

Keywords Automatic surgical planning $\cdot \cdot$ Reverse shoulder arthroplasty $\cdot \cdot$ Shoulder joint diseases $\cdot \cdot$ Prosthesis implantation

# Introduction

In recent years, there has been an increase in the prevalence of shoulder joint problems [1-3]. Frequent shoulder joint diseases, such as massive irreparable rotator cuff tears, comminuted proximal humeral fracture, etc., are currently the most challenging diseases to be cured [4]. Reverse shoulder arthroplasty (RSA) can provide an efficient treatment for shoulder joint diseases [5].

However, there are still many limitations in RSA. One of the most challenging problems is the loss of motion freedom, especially the internal rotation after RSA [6-9]. Preoperative planning is an effective method to clinically reduce the postoperative risk. Currently, the preoperative planning of RSA is mainly accomplished by experienced surgeons manually, including determining the position of the prosthesis base and the direction of the screws. The effect of the RSA surgery is highly determined by the results of the preoperative planning. Therefore, efficacious automatic preoperative planning is of great significance.

The state-of-the-art computer-aided surgery (CAS) technology has been applied to this field [10, 11]. Moreschini et al. [12] combined preoperative CT-based planning with intraoperative navigation to improve the positioning and fixation of the baseplate and screws. Sabesan et al. [13] compared three different preoperative planning software and found them effective to improve accuracy and to correct pathological bone loss. Parsons et al. [14] used different methods to place the prosthesis, which could correct and optimize the implant placement stably. However, these researches mainly focused on improving the accuracy, which is still an empirical value from the previous operation approaches. Without an effective and unified quantitative evaluation method, the planning results are mainly determined by the surgeon according to their own experience. As a result, the preoperative surgical planning for RSA becomes a labor-intensive and time-consuming task, which has severely restricted the development of RSA.

To solve these problems, an automatic method of RSA preoperative planning is proposed in this study using bone density assessment and path integral in cone space. On this basis, a special software for automatic RSA planning was developed, through which planning of RSA could be realized efficiently and accurately.

# Materials and methods

The prosthesis (Bigliani/Flatow, Model No. 00-4349-036- 11, Zimmer Biomet, Inc., US), shown in Fig. 1, includes a prosthesis base with a peg and two long screws, among which, the peg is fixed to the prosthesis base, while two long screws can be rotated around the implantation points on the prosthesis base by $\gamma$ . The implant holes of the long screws are centered symmetrically with the middle peg.

The task of RSA preoperative planning includes positioning the prosthesis and fixing it with screws. Since positioning the base prosthesis with a shorter peg has a relatively high error tolerance and costs just a little amount of time in the entire typical conventional planning process, it is still acceptable by manual operation. Therefore, the major optimization target of our research is the planning involving two long screws. According to clinical experience, long screws should pass through areas with greater bone density for larger fixing force. Poor planning will cause loosen screws or even injury to the surrounding tissue. Therefore, ensuring the long screw paths located in the area with high bone density without exceeding the bone boundary is the key to the automatic planning.

Our method includes both interactive reference point selection and automatic optimal path searching as illustrated in the workflow of Fig. 1.

# Interactive operation

The main function of the interactive operation is to determine the position of the prosthesis. It includes reconstructing a 3D model, picking reference points and building a working plane. In 3D model reconstruction, the Marching Cubes algorithm [15] is applied to perform 3D reconstruction of the isosurface extracted with a threshold $\psi$ . Then in the section of picking reference points, 3 points $p _ { 1 } , p _ { 2 }$ and $p _ { 3 }$ are picked to determine the positioning plane of the base as shown in (1-4) of Fig. 2a. Subsequently, another point $p _ { 4 }$ is picked to limit the rotation of the prosthesis, where $p _ { 1 }$ is the positioning point of the middle peg and $p _ { 2 } , p _ { 3 }$ are combined with $p _ { 1 }$ to generate the positioning plane of the prosthesis to obtain the normal vector $\pmb \varepsilon$

To ensure the correct placement of the prosthesis, $\pmb \varepsilon$ should point from the scapula plane towards the torso. Generally, there is a gap between the scapula and the humerus, inside which the bone density is much smaller. This feature can be used to correct the direction of $\pmb \varepsilon$

Two candidate paths $< p _ { 1 }$ $p _ { m 1 } >$ and $< p _ { 1 }$ $p _ { m 2 } >$ of the middle peg are generated by Eqs. (1) and (2). Then the bone density integration is performed, respectively, on them. The direction of $\pmb \varepsilon$ can be determined by Eq. (3), which is also the direction of the middle peg.

$$
p _ { m 1 } = p _ { 1 } + l _ { m } \pmb { \varepsilon }
$$

$$
p _ { m 2 } = p _ { 1 } - l _ { m } \pmb { \varepsilon }
$$

$$
\pmb { \varepsilon } = \left\{ \begin{array} { l l } { \varepsilon , \quad \mathrm { i f } \displaystyle \int _ { p _ { 1 } } ^ { p _ { m 1 } } v d p > \displaystyle \int _ { p _ { 1 } } ^ { p _ { m 2 } } v d p } \\ { - \varepsilon , \mathrm { e l s e } } \end{array} \right.
$$

where $p _ { m 1 } , ~ p _ { m 2 }$ are the respective endpoints of each path and $v$ represents the grayscale value at point $p$

From above, the position of the base and the path of the middle peg have been determined. However, the prosthesis can still rotate around the middle peg as shown in Fig. 2b. Therefore, $p _ { 4 }$ is picked to compute a unit direction vector $\mathbf { \delta } _ { m }$ by Eq. (4) as shown in (5) of Fig. 2a. $p { 5 }$ and $p _ { 6 }$ can be obtained as the starting points of the two long screws by

![](images/6dbe4b0101f54e8827995dadec49a5c96ea0385c4202a959d36a58f0eadc6f18.jpg)  
Fig. 1 Workflow of RSA preoperative planning, including interactive reference point selection and automatic optimal path searching

![](images/661cca2fe195d48b6df1e6efe399181e1539983b23f229728dcd21aaaf4bdc96.jpg)  
Fig. 2 a the point-picking steps in the planning process. b the rotate DOF of the prosthesis base

Eqs. (5) and (6), as depicted in (6) of Fig. 2a.

$$
\begin{array} { c } { m = \displaystyle \frac { p _ { 4 } - p _ { 1 } } { \| p _ { 4 } - p _ { 1 } \| } } \\ { p _ { 5 } = p _ { 1 } + \frac { \rho } { 2 } m } \\ { p _ { 6 } = p _ { 1 } - \frac { \rho } { 2 } m } \end{array}
$$

where $p _ { 1 }$ is the first point selected by the surgeon. $\rho$ is the measured distance between the implant holes of the two long screws in the prosthesis model.

# Automatic planning

The main function of automatic surgical planning is to determine the position and orientation of the two long screw paths with the parameters obtained during the interactive operation. As shown in Fig. 1, it includes generating active cone region, alternative paths, and optimal solutions. The pseudo-code is shown in Algorithm 1, where CT_data refers to the original CT data. $\alpha$ is an angle constraint value that is close to the actual value $\gamma . R _ { a } , R _ { l } , R _ { i }$ are radial resolution, circumferential resolution and integration resolution respectively. Threshold value $\psi$ is used to add exposure constraint. $\rho$ is the distance between $p { 5 }$ and $p _ { 6 } . l , r$ are the length and the radium of long screws. $l _ { m }$ is the length of the middle peg.

Algorithm 1: Automatic Planning   
Input: $p , \ \pmb { \varepsilon } , \ l , \ \eta , \ m , \ \alpha , \ R _ { a } , \ R _ { l } , \ R _ { i } ,$ CT_data   
Output: the vector of screw path $\pmb { P } ^ { * }$   
1: $\begin{array} { l } { n  l * \varepsilon + \eta * m } \\ { b a s e C e t e r  p + n } \\ { \beta  2 \pi / R _ { a } } \\ { r _ { b }  l * t a n \alpha } \end{array}$   
2:   
3:   
4:   
5: $\pmb { \theta } [ 3 ] [ 3 ] $ getRotateMatrix $( r _ { b } , \pmb { n } , \beta )$ by equation (11), ( 12 ) and (13)   
6: $\pmb { e } \gets ( \pmb { \varepsilon } [ 2 ] , 0 , \pmb { \varepsilon } [ 0 ] )$   
7: Normalize(e)   
8: $n u m \gets R _ { a } * R _ { l }$   
9: basePoints[num $] [ 3 ]  \{ 0 \}$   
10: For i in range(0, num):   
11: basePoir $\mathbf { \ell } \mathbf { \ell } \mathbf { \cdot } \mathbf { \ell } \mathbf { \mathit { i t s } } [ i ] \gets \mathit { b a s e C e t e r } + \mathit { r _ { b } } * e$   
12: For j in range $( 0 , R _ { l } )$ ..   
13: 14: $\begin{array} { l } { i + + } \\ { b a s e P o i n t s [ i ]  b a s e C e t e r \ + \ j * r _ { b } * e / R _ { l } } \end{array}$   
15: End for   
16: $\pmb { e } \gets \pmb { e } * \pmb { \theta } [ 3 ] [ 3 ]$   
17: End for   
18: k\* k: argmax cbasePoints[k] vdp)   
k E{0,1,2,.,num}   
20: P\*  (p,basePoints[k\*])   
21: return P\*

The first step is to generate the active cone region. As mentioned above, long screws can rotate around $p { 5 }$ or $p _ { 6 }$ , and the rotation space of the long screw is of cone-shape, inside which the optimal path must be located. To ensure better stability and prevent interference between screws, two long screws usually diverge from each other in clinical applications [16]. Therefore, the direction of the cone should also be adjusted accordingly. An offset value $\eta$ , the direction vectors $\pmb { n } _ { 1 }$ and ${ \pmb n } _ { 2 }$ , and the radium $r _ { c }$ can be obtained by Eqs. (7-10). Then, ${ \pmb n } _ { 1 }$ is used as the vector from the top of the cone to the center of the bottom surface, $p _ { 5 }$ as the apex of the cone, and $r _ { c }$ as the bottom radius to construct a cone space. $n _ { 2 } , p _ { 6 }$ and $r _ { c }$ are used to construct another cone space. The cone space in the scapula is shown in Fig. 3a.

$$
\eta = l \cdot \sin ( \alpha )
$$

$$
\pmb { n } _ { 1 } = l \pmb { \varepsilon } + \eta \pmb { m }
$$

$$
r _ { c } = l \cdot \tan ( \alpha )
$$

where $\eta$ represents the offset. $\alpha$ is the constraint angle of the prosthesis. $l$ is the length of long screws and $\mathbf { \delta } _ { m }$ is generated by Eq. (4).

To generate alternative paths in the cone space, a $3 \times 3$ rotation matrix $\pmb { \Theta }$ is constructed by Eqs. (11-13).

$$
\pmb { \theta } = \left[ \begin{array} { l l l } { \theta _ { 1 1 } } & { \theta _ { 1 2 } } & { \theta _ { 1 3 } } \\ { \theta _ { 2 1 } } & { \theta _ { 2 2 } } & { \theta _ { 2 3 } } \\ { \theta _ { 3 1 } } & { \theta _ { 3 2 } } & { \theta _ { 3 3 } } \end{array} \right]
$$

$$
\theta _ { i j } = U [ i - 1 ] U [ j - 1 ] ( 1 - \cos ( \beta ) ) + \Delta ( i , j )
$$

$$
\Delta ( i , j ) = \left\{ \begin{array} { l l } { - \cos ( \beta ) , } & { i = j } \\ { ( - 1 ) ^ { \frac { i - j } { \left| i - j \right| } - ( - 1 ) ^ { j - i } } U [ 5 - i - j ] \sin ( \beta ) , \mathrm { e l s e } } \end{array} \right.
$$

where $\beta = 2 \pi / R _ { a }$ , and ${ \cal R } _ { a }$ is the circumferential resolution. $\pmb { U }$ is the unit direction vector of the cone, which represents ${ \pmb n } _ { 1 } ( { \pmb n } _ { 2 } )$

Next, a unit vector $\scriptstyle e$ was set in the bottom surface of the cone arbitrarily. The center of the bottom surface $p _ { 7 }$ is computed by $( p _ { 5 ( 6 ) } + { \pmb n } _ { 1 ( 2 ) } )$ . A point on the circumference of the bottom surface can be calculated by $( p _ { 7 } + r _ { c } { \pmb e } )$ . By connecting the point with $p _ { 7 }$ and dividing the connection into $R _ { l }$ equal parts, $( R _ { l } - 1 )$ line segments can be obtained. Then the vector $e$ is updated by $e _ { n e w } = \Theta e$ to let $e$ rotate around $\pmb { U }$ by $\beta$ . Repeating the operation for ${ \cal R } _ { a }$ times can thus generate evenly distributed points on the bottom surface.

As shown in Fig. 3b, all the points in the bottom surface of the cone are regarded as the endpoint of each candidate path and the apex of the cone is the start point. Then, $R _ { a } R _ { r } + 1$ candidate paths can be generated. Equidistant points on each candidate path are selected according to the value of $R _ { i }$ . In this way, the total number of points $N _ { \mathrm { p o i n t } }$ can be calculated by Eqs. (14) as:

$$
N _ { \mathrm { p o i n t } } = N _ { \mathrm { p a t h } } R _ { i }
$$

where $N _ { \mathrm { p a t h } } = R _ { a } R _ { l } + 1 . R _ { a }$ is the circumferential resolution, $R _ { l }$ is the radial resolution, and $R _ { i }$ is integration resolution.

Afterwards, the coordinate transformation is used to obtain the corresponding grayscale value $\boldsymbol { v }$ in the original CT. To solve the screw exposure problem and to save computation resources, the path integral is performed synchronously with the following analytical methods:

An $N _ { \mathrm { p a t h } }$ -dimensional vector $\Omega$ containing safety sign $\omega$ of the corresponding path is defined with all $\omega$ predefined as 1. In CT images, the grayscale values appear to be larger when the density is higher. Since the scapular bone of humans has a much higher density compared to surrounding tissue, if the grayscale value of points in the candidate path is significantly less than the aforementioned threshold value $\psi$ , it can be inferred that the path has crossed the boundary of the bone, which means the screw may penetrate the soft tissue if following this path. Then the corresponding $\omega$ will be set to 0.

However, the path is currently treated as an ideal straight line without thickness. Situations would happen when the centerline of the prosthetic screw may be still inside the bone, but the surface of the screw can be exposed outside the bone boundary, which is not considered to be available planning results. To optimize this condition, the volume constraint of the screw is appended to the algorithm. A rotation matrix $\pmb { \theta } _ { 1 }$ is constructed by Eqs. (11-13), with the unit direction vector of the path to replace $\pmb { U }$ , the radius of the screw $r$ to replace $r _ { c }$ , and a resolution angle $\chi$ to replace $\beta$ . Then the previous steps are repeated to generate points around the ideal path. All these points are evenly distributed on the surface of the minimum enclosing cylinder of the screw. If these points cannot meet the requirement, part of the surface of the screw will be exposed outsides. Figure 3c shows the situation of adding volume constraints or not in 3 cases.

Finally, those paths with $\omega = 0$ will be deleted, which can save time and computation resources. The path with the largest integral value will be the optimal solution with the greatest stability. The calculation process is shown in Eqs. (15-17).

$$
\begin{array} { r l } & { \Lambda = \left\{ \lambda _ { 1 } , \lambda _ { 2 } , \lambda _ { 3 } , . . . , \lambda _ { N _ { \mathrm { p a t h } } } \right\} : } \\ & { \lambda _ { j } = \left\{ p _ { j 1 } , p _ { j 2 } , p _ { j 3 } , . . . , p _ { j R _ { i } } \right\} } \end{array}
$$

![](images/3fde17e9bbe3b47fb862ad17d8fb8bf1b1967a87f57699b7eb64fb325679c847.jpg)  
Fig. 3 a Cones in the scapular. b Schematic diagram of the path in cone space. The blue line segments represent the alternative paths, and the green points represent the endpoints of the paths. c The comparison in

$$
\Omega = \{ \omega _ { 1 } , \omega _ { 2 } , \omega _ { 3 } , \ldots , \omega _ { N _ { \mathrm { p a t h } } } \} :
$$

$$
\omega _ { j } = \left\{ \begin{array} { l l } { 0 , \forall _ { k = 0 } ^ { R _ { i } } v _ { p _ { k \in \lambda _ { j } } } < \psi } \\ { 1 , \forall _ { k = 0 } ^ { R _ { i } } v _ { p _ { k \in \lambda _ { j } } } \geq \psi } \end{array} \right.
$$

$$
\lambda ^ { * } = \operatorname * { a r g m a x } _ { \substack { \lambda _ { 1 } , \lambda _ { 2 } , \lambda _ { 3 } , \ldots , \lambda _ { j } \in \Lambda } } \left( \sum _ { k = 0 } ^ { R _ { i } } v _ { p _ { j k \in \lambda _ { j } } } | \omega _ { j } = 1 \right)
$$

three cases. The volume constraint is not considered for the upper ones, and the volume constraint with $6 0 ^ { \circ }$ as angular resolution is added for the lower ones

where Ais a collection of $N _ { \mathrm { p a t h } }$ candidate paths. $\Omega$ is the safety sign vector of candidate paths, while $\omega = 0$ means unsafe and 1 means safe.

# Experiments and results

According to the above-mentioned planning algorithm, an automatic planning software for RSA was developed using

![](images/9b62537559c42d2fe1563301813e4bf07a197c8341f0bc15bdfe7b6d1d99fcbf.jpg)  
Fig.4 The usr  scrsho f th dop tre, d thtion pane onthe et andstbl indws of  rndring transverse, sagittal, and coronal views on the right

Visual Studio 2019, Qt5.9, and Visualization Toolkit 8.2. The user interface of the software is shown in Fig. 4. The operation panel with 6 DOF (Degree of Freedom) fine-tuning function is designed in the software to refine the position of the prosthesis base for the final result. The software can assist doctors, engineers, and scientific researchers in terms of achieving the automatic surgical planning of RSA quickly and effectively.

To better evaluate the performance of our algorithm, two experiments were conducted. In Experiment 1, preoperative surgical planning on different RSA cases was performed to verify the quality and efficiency of the planning algorithm. In Experiment 2, repetitive operations of preoperative surgical planning on the same case were performed to verify the stability of the algorithm against the interference of the position deviation of the reference points selection. The experiment data were from Shanghai Jiao Tong University Affiliated Sixth People's Hospital, including $2 5 \ \mathrm { C T }$ of RSA patients and a standard prosthesis. The average age of the patients is 61 years old, and $44 \%$ of them are male. The image data dimensions are $5 1 2 \times 5 1 2 \times 2 7 1 \sim 5 1 2 \times 5 1 2 \times 4 6 5$ and $7 6 8 \times 7 6 8 \times 1 1 0 \sim 7 6 8 \times 7 6 8 \times 1 8 6$ . The spacing ranges from $0 . 3 5 8 1 \ \mathrm { m m } \times 0 . 3 5 8 1 \ \mathrm { m m } \times 0 . 6 2 5 \ \mathrm { m m }$ to $0 . 9 7 6 6 \mathrm { m m }$ $\times \ 0 . 9 7 6 6 \ \mathrm { m m } \times 1 . 5 \ \mathrm { m m }$ . For the prosthesis, the length of the middle peg and long screws are $1 4 . 7 \mathrm { m m }$ and $3 8 . 9 7 \mathrm { m m }$ . The radius of long screws is $1 . 6 5 ~ \mathrm { m m }$ , and the distance of implant holes on the base is $1 2 . 9 7 \mathrm { m m }$ . All experiments were performed with the aforementioned software running on a computer with i5-10210u as CPU and MX250 as GPU.

# The evaluation of planning quality and efficiency

Twenty-four RSA cases were selected for Experiment 1. $R _ { a } , R _ { l } , R _ { i }$ were set to be 150, 30, $3 0 ~ \mathrm { m m }$ . The number of surface points around each point on the candidate path for volume constraint is 6. Then the constraints angle $\alpha$ , which is adjustable to change the strength of constraints, was set as $4 5 ^ { \circ }$ based on the measurement on the 3D printed prosthesis. Other specific parameters were determined by the actual prosthesis model. The assessment criterion included the angles between the final paths $\theta _ { 1 }$ $\theta _ { 2 }$ $\theta _ { 3 }$ , bone density ratio $\delta$ and planning time $t _ { 1 } , t _ { 2 } , t _ { 3 } , t .$ Among them, $\theta _ { 1 }$ and $\theta _ { 2 }$ represent the angles between long screws and middle peg respectively. $\theta _ { 3 }$ represents the angle between two long screws. $t _ { 1 } , t _ { 2 } , t _ { 3 }$ and $t$ represent the time for reconstructing 3D bone model, picking reference points, auto planning, and total time respectively. 8 represents the ratio of the integral value along the path generated by our algorithm to the path with the maximum bone density integral, without considering the screw exposure in the entire conical space. The closer $\delta$ is to 1, the higher stability the prosthesis will have after planning under various constraints.

The results are shown in Table 1. In actual scenarios, the planning results are acceptable when $\theta _ { 1 }$ and $\theta _ { 2 }$ do not exceed the range of O to $\alpha$ . Table 1 shows that our algorithm can meet this requirement. In terms of time consumption, the time for reconstructing 3D scapular $t _ { 1 }$ is related to the image volume of CT, and the time for interactive operations $t _ { 2 }$ varies the most, because the time to select suitable reference points on dissimilar scapulars is different. All cases can be planned within $3 ~ \mathrm { m i n }$ with an average time of 83.96 s. It should be noted that the calculation time of the automatic planning algorithm $t _ { 3 }$ only takes $4 . 3 9 ~ \mathrm { s } .$ This indicates that automatic planning of RSA with high efficiency can be completed through the proposed algorithm. The average value of $\delta$ is 0.9492, and the variance is 0.013, which proves that our algorithm can achieve satisfactory results in different cases.

Table 1 Results of Experiment 1   

<html><body><table><tr><td rowspan="2">Case</td><td rowspan="2">Gender</td><td rowspan="2">Age</td><td rowspan="2"> Image volume (pixel)</td><td rowspan="2">8(%)</td><td colspan="3">Angle ()</td><td colspan="4">Time (s)</td></tr><tr><td>01  45</td><td>02  45</td><td>03</td><td>t1</td><td></td><td>t3</td><td>t</td></tr><tr><td>1</td><td>Male</td><td>58</td><td>512 x 512 x 307</td><td>95.27</td><td>18.07</td><td>34.97</td><td>46.03</td><td>20.59</td><td>25.80</td><td>4.23</td><td>50.62</td></tr><tr><td>2</td><td> Female</td><td>61</td><td>768  768 x 110</td><td>95.06</td><td>11.57</td><td>15.33</td><td>24.62</td><td>17.01</td><td>31.64</td><td>3.67</td><td>52.32</td></tr><tr><td>3</td><td>Female</td><td>57</td><td>512 x 512 x 289</td><td>100</td><td>27.60</td><td>19.41</td><td>46.37</td><td>16.98</td><td>20.58</td><td>7.45</td><td>45.01</td></tr><tr><td>4</td><td>Male</td><td>71</td><td>512 x 512 x 271</td><td>99.89</td><td>33.92</td><td>31.49</td><td>57.84</td><td>19.03</td><td>64.32</td><td>4.47</td><td>87.82</td></tr><tr><td>5</td><td>Male</td><td>56</td><td>512 x 512 x 297</td><td>98.09</td><td>21.64</td><td>15.66</td><td>35.95</td><td>18.04</td><td>90.24</td><td>4.91</td><td>113.19</td></tr><tr><td>6</td><td>Male</td><td>43</td><td>768 x 768  186</td><td>93.83</td><td>31.08</td><td>20.75</td><td>49.89</td><td>23.48</td><td>30.30</td><td>4.16</td><td>57.94</td></tr><tr><td>7</td><td>Male</td><td>72</td><td>512 x 512 x 425</td><td>89.39</td><td>19.44</td><td>42.76</td><td>45.87</td><td>26.08</td><td>95.77</td><td>3.26</td><td>125.11</td></tr><tr><td>8</td><td>Female</td><td>51</td><td>512 x 512  321</td><td>97.77</td><td>19.38</td><td>23.49</td><td>40.84</td><td>21.52</td><td>70.42</td><td>4.53</td><td>96.47</td></tr><tr><td>9</td><td>Female</td><td>74</td><td>512 x 512 x 385</td><td>98.92</td><td>23.01</td><td>15.89</td><td>38.86</td><td>19.41</td><td>80.25</td><td>4.58</td><td>104.24</td></tr><tr><td>10</td><td>Male</td><td>49</td><td>512 x 512 x 307</td><td>97.59</td><td>28.52</td><td>28.16</td><td>48.67</td><td>24.40</td><td>34.47</td><td>4.40</td><td>63.27</td></tr><tr><td>11</td><td>Female</td><td>69</td><td>512 x 512 x 289</td><td>99.54</td><td>28.87</td><td>27.11</td><td>55.74</td><td>20.02</td><td>84.37</td><td>4.84</td><td>109.23</td></tr><tr><td>12</td><td>Male</td><td>62</td><td>768 x 768  160</td><td>100</td><td>27.86</td><td>24.79</td><td>50.48</td><td>19.77</td><td>65.54</td><td>3.96</td><td>89.27</td></tr><tr><td>13</td><td> Female</td><td>46</td><td>512  512 x 465</td><td>100</td><td>29.04</td><td>27.46</td><td>55.14</td><td>22.11</td><td>45.20</td><td>3.61</td><td>70.92</td></tr><tr><td>14</td><td>Female</td><td>66</td><td>512 x 512 x 271</td><td>99.81</td><td>21.80</td><td>18.60</td><td>38.72</td><td>26.59</td><td>30.02</td><td>4.55</td><td>61.16</td></tr><tr><td>15</td><td>Female</td><td>65</td><td>768 x 768 x 140</td><td>87.24</td><td>23.39</td><td>27.92</td><td>43.89</td><td>17.45</td><td>63.73</td><td>4.36</td><td>85.54</td></tr><tr><td>16</td><td>Male</td><td>66</td><td>512 x 512 x 289</td><td>87.29</td><td>19.44</td><td>20.56</td><td>37.92</td><td>21.33</td><td>108.14</td><td>4.58</td><td>134.05</td></tr><tr><td>17</td><td>Male</td><td>75</td><td>768  768  160</td><td>96.26</td><td>13.52</td><td>17.7</td><td>23.17</td><td>17.28</td><td>67.19</td><td>4.33</td><td>88.80</td></tr><tr><td>18</td><td>Female</td><td>63</td><td>512 x 512 x 305</td><td>85.10</td><td>29.38</td><td>18.87</td><td>44.08</td><td>24.93</td><td>137.68</td><td>4.82</td><td>167.43</td></tr><tr><td>19</td><td>Female</td><td>62</td><td>512 x 512 x 321</td><td>98.29</td><td>13.36</td><td>26.05</td><td>36.99</td><td>17.32</td><td>31.12</td><td>4.04</td><td>52.48</td></tr><tr><td>20</td><td>Female</td><td>54</td><td>512 x 512 x 289</td><td>99.26</td><td>20.65</td><td>19.39</td><td>38.62</td><td>18.39</td><td>72.49</td><td>5.35</td><td>96.23</td></tr><tr><td>21</td><td>Male</td><td>52</td><td>512 x 512 x 280</td><td>99.35</td><td>11.19</td><td>32.77</td><td>40.91</td><td>16.80</td><td>74.89</td><td>4.18</td><td>95.87</td></tr><tr><td>22</td><td> Female</td><td>58</td><td>512 x 512 x 280</td><td>73.67</td><td>16.42</td><td>31.27</td><td>39.35</td><td>16.40</td><td>48.57</td><td>4.01</td><td>68.98</td></tr><tr><td>23</td><td>Male</td><td>57</td><td>512 x 512 x 271</td><td>88.94</td><td>15.44</td><td>20.89</td><td>35.26</td><td>18.18</td><td>29.48</td><td>3.58</td><td>51.24</td></tr><tr><td>24</td><td>Female</td><td>75</td><td>512  512  413</td><td>97.44</td><td>26.67</td><td>23.47</td><td>45.49</td><td>24.21</td><td>20.12</td><td>3.55</td><td>47.88</td></tr><tr><td>Aver</td><td>N/A</td><td>61</td><td>N/A</td><td>94.92</td><td>N/A</td><td>N/A</td><td>N/A</td><td>20.31</td><td>59.26</td><td>4.39</td><td>83.96</td></tr></table></body></html>

To further demonstrate the efficiency of the proposed algorithm, three cases shown in Fig. 5 are randomly selected. It shows that our algorithm takes the initiative to search for the area with higher bone density while ensuring that the screws are inside the bone.

# The evaluation of stability

The preoperative planning on one case were conducted 10 times in Experiment 2. The CT dimension was $5 1 2 \times 5 1 2$ $\times 3 7 1$ from a 63-year-old male. The parameters remained the same as in Experiment 1. There were deviations in the reference points selected by the operator each time. However, it shows that $\theta _ { 1 }$ and $\theta _ { 2 }$ still range within the limits from 0 to $\alpha$ in Table 2. The average of $\delta$ is 0.9787 and the variance is O.00027. This demonstrates that even if there is a specific disturbance in the input, the planning paths can still maintain high quality, which proves the excellent stability of our algorithm, and can significantly reduce the technical requirements for operators.

![](images/358744f1440a975c65ee484cc273965011abf34713926dfcc82fd14c347a8c39.jpg)  
Fig. 5 The results of automatic planning for three RSA cases, includ- blue line represents the contour of the prosthesis base, and the yellow ag two right shoulders and one left shoulder. From left to right are the line represents the contour of the long screw. The areas in the red circles lanning results from 3D, transverse, sagittal, and coronal views. The are with low bone density

Table 2 Result of Experiment 2   

<html><body><table><tr><td rowspan="2">Case</td><td colspan="3">Angle ()</td><td rowspan="2">8 (%)</td></tr><tr><td>01  45</td><td>02  45</td><td>03</td></tr><tr><td>1</td><td>23.00</td><td>23.61</td><td>46.60</td><td>97.95</td></tr><tr><td>2</td><td>20.95</td><td>26.57</td><td>47.17</td><td>97.64</td></tr><tr><td>3</td><td>24.54</td><td>20.79</td><td>45.32</td><td>97.54</td></tr><tr><td>4</td><td>34.61</td><td>11.64</td><td>45.86</td><td>97.02</td></tr><tr><td>5</td><td>20.62</td><td>27.15</td><td>47.46</td><td>98.87</td></tr><tr><td>6</td><td>20.50</td><td>25.46</td><td>44.35</td><td>98.14</td></tr><tr><td>7</td><td>18.94</td><td>30.07</td><td>48.10</td><td>98.63</td></tr><tr><td>8</td><td>27.15</td><td>22.60</td><td>49.74</td><td>97.73</td></tr><tr><td>9</td><td>25.90</td><td>24.99</td><td>48.42</td><td>97.67</td></tr><tr><td>10</td><td>27.37</td><td>16.82</td><td>43.96</td><td>97.52</td></tr><tr><td>Aver</td><td>N/A</td><td>N/A</td><td>N/A</td><td>97.87</td></tr><tr><td>Var</td><td>N/A</td><td>N/A</td><td>N/A</td><td>0.027</td></tr></table></body></html>

# Discussion

Compared with other state-of-the-art preoperative planning solutions, there are unique advantages in our algorithm. In the research presented by Parsons et al. [14], the result of preoperative planning may be quite different among different physicians and surgeons in some cases. However, from the analysis of the results in our Experiment 2, the noise caused by the deviation of reference point selection has little influence on the final planning result. This reduces the individual error caused by the surgeon's subjective judgment and can meet the needs of many people. In the research of Moreschini et al. [12] and Venne et al. [17], tedious operations and constant adjustments are required to accurately determine the position of the prosthesis. Compared with their approaches, our proposed method allows the planning of an ideal path in an average time of no more than $1 . 5 \mathrm { m i n }$ , while above-mentioned traditional surgical planning methods cannot ensure that the screw path is located in the area with high bone density, which may affect the stability of the prosthesis.

Although our method enables efficient and automatic planning for RSA, there are still some limitations. For example, fully automated planning hasn't been realized, and the reference points still need to be picked manually, which nevertheless is with high error tolerance and quite simple. In addition, for some special cases of scapulars with severe deformation or disordered bone density distribution, it may not achieve ideal results. Currently, only Zimmer prosthesis is used in our study, for the prosthesis manufactured by other companies, the relevant geometric parameters should be adapted accordingly. Therefore, further research to optimize the planning scheme should be conducted.

# Conclusion

Focusing on the problems of low efficiency, high technical requirements, and unstable effects in the preoperative planning of RSA, we proposed an automatic surgical planning method based on bone density assessment and path integral in cone space. The surgeon only needs to select several reference points on the 3D model as the inputs, and then through the path integral of bone density, the optimal screw path can be automatically planned within 5 s. We have also integrated our algorithms into an automatic planning software for RSA. The final experiment shows that our method can not only realize automatic planning quickly and accurately but also achieve relatively good results in terms of efficiency, stability, and applicability, which will have great potential clinical applications in the future.

Acknowledgements This work was supported by grants from the National Natural Science Foundation of China (81971709; M-0019;

82011530141), the Foundation of Science and Technology Commission of Shanghai Municipality (19510712200; 20490740700), Shanghai Jiao Tong University Foundation on Medical and Technological Joint Science Research (YG2019ZDA06; YG2021ZD21; YG2021QN72; YG2022QN056), the Program of Shanghai Academic/Technology Research Leader (19XD1402800), the Shanghai Municipal Education Commission Scientific Research and Innovation Program (2021-01-07- 00-02-E00082), the Clinical Research Center of Shanghai University of Medicine & Health Sciences (20MC2020003), the Academician Expert Workstation of the Jinshan District (jszjz2020007Y), the Plan of Medical Key Specialty Construction, and the Shanghai Health Committee (ZK2019B03).

# Declarations

Conflict of interest The authors declare that they have no conflict of interest.

Ethical approval All procedures performed in studies involving human participants were in accordance with the ethical standards of the institutional and/or national research committee and with the 1964 Helsinki Declaration and its later amendments or comparable ethical standards.

Informed consent There was no informed consent required for the work reported in this manuscript.

# References

1. Best MJ, Aziz KT, Wilckens JH, McFarlnd EG, Srikumaran U (2021) Increasing incidence of primary reverse and anatomic total shoulder arthroplasty in the United States. J Shoulder Elb Surg 30:1159-1166   
2. Aleem AW, Feeley BT, Austin LS, Ma CB, Krupp RJ, Ramsey ML, Getz CL (2017) Effect of humeral component version on outcomes in reverse shoulder arthroplasty. Orthopedics 40(3):179-186   
3. Cox RM, Padegimas EM, Abboud JA, Getz CL, Lazarus MD, Ramsey ML, Williams GR, Horneff JG (2018) Outcomes of an anatomic total shoulder arthroplasty with a contralateral reverse total shoulder arthroplasty. J Shoulder Elb Surg 27(6):998-1003   
4. Boyle MJ, Youn SM, Frampton CMA, Ball CM (2013) Functional outcomes of reverse shoulder arthroplasty compared with hemiarthroplasty for acute proximal humeral fractures. J Shoulder Elb Surg 22(1):32-37   
5. Rodriguez JA, Entezari V, Iannoti JP, Ricchetti ET (2019) Preoperative planning for reverse shoulder replacement: the surgical benefits and their clinical translation. Ann Joint 4:4   
6. Mellano CR, Kupfer N, Thorsness R, Chalmers PN, Feldheim TF, O'Donnell P, Cole BJ, Verma NN, Romeo AA, Nicholson GP (2017) Functional results of bilateral reverse total shoulder arthroplasty. J Shoulder Elb Surg 26(9):990-996   
7. Latif V, Denard PJ, Young AA, Liotard JP, Walch G (2012) Bilateral anatomic total shoulder arthroplasty versus reverse shoulder arthroplasty. Orthopedics 35(4):479-485   
8. Triplet JJ, Everding NG, Levy JC, Moor MA (2015) Functional internal rotation after shoulder arthroplasty: a comparison of anatomic and reverse shoulder arthroplasty. J Shoulder Elb Surg 24(6):867-874   
9. Wirth B, Kolling C, Schwyzer HK, Flury M, Audige L (2016) Risk of insufficient internal rotation after bilateral reverse shoulder arthroplasty: clinical and patient-reported outcome in 57 patients. J Shoulder Elb Surg 25(7):1146-1154   
10. Boileau P, Cheval D, Gauci MO, Holzer N, Chaoui J, Walch G (2018) Automated three-dimensional measurement of glenoid version and inclination in arthritic shoulders. J Bone Joint Surg Am 100(1):57-65   
11. Stephens SP, Paisley KC, Giveans MR, Wirth MA (2015) The effect of proximal humeral bone loss on revision reverse total shoulder arthroplasty. J Shoulder Elb Surg 24(10):1519-1526   
12. Moreschini F, Colasanti GB, Cataldi C, Manneil L, Mondanelli N, Giannotti S (2020) Pre-operative CT-based planning integrated with intra-operative navigation in reverse shoulder arthroplasty: data acquisition and analysis protocol, and preliminary results of navigated versus conventional surgery. Dose-Response. https://doi. org/10.1177/1559325820970832   
13. Sabesan VJ, Lima DJL, Rudraraju RT, Wilneff M, Sheth B, Yawman J (2020) Reliability and accuracy of 3D preoperative planning software for glenoid implants in total shoulder arthroplasty. Semin Arthroplasty JSES 30(4):375-382   
14. Parsons M, Greene A, Polakovic S, Byram I, Cheung E, Jones R, Papandrea R, Youuderian A, Wright T, Flurin P, Zuckerman J (2020) Assessment of surgeon variability in preoperative planning of reverse total shoulder arthroplasty: a quantitative comparison of 49 cases planned by 9 surgeons. J Shoulder Elb Surg 29(10):2080-2088   
15. Kim NH, Yoo SK, Lee KS (2003) Polygon reduction of 3D objects using Stokes' theorem. Comput Methods Programs Biomed 71(3):203-210   
16. Smithers CJ, Yooung AA, Walch G (2011) Reverse shoulder arthroplasty. Curr Rev Musculoskelet Med 4(4):183-190   
17. Venne G, Rasquinha BJ, Pichora D, Ellis RE, Bicknell R (2015) Comparing conventional and computer-assisted surgery baseplate and screw placement in reverse shoulder arthroplasty. J Shoulder Elb Surg 24(7):1112-1119

Publisher's Note Springer Nature remains neutral with regard to jurisdictional claims in published maps and institutional affiliations.