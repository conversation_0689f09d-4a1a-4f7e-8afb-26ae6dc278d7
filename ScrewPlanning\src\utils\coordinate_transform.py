"""
坐标系转换工具

处理不同医学图像坐标系之间的转换，特别是RAS和LPS坐标系
"""

import logging
from typing import Dict, List, Tuple
from dataclasses import dataclass

try:
    from .geometry import Point3D, Vector3D
except ImportError:
    from geometry import Point3D, Vector3D


@dataclass
class CoordinateSystem:
    """坐标系定义"""
    name: str
    description: str
    x_axis: str  # 'R' (Right) or 'L' (Left)
    y_axis: str  # 'A' (Anterior) or 'P' (Posterior)
    z_axis: str  # 'S' (Superior) or 'I' (Inferior)


class CoordinateTransformer:
    """坐标系转换器"""
    
    # 预定义的坐标系
    RAS = CoordinateSystem("RAS", "Right-Anterior-Superior", "R", "A", "S")
    LPS = CoordinateSystem("LPS", "Left-Posterior-Superior", "L", "P", "S")
    
    def __init__(self):
        """初始化坐标转换器"""
        logging.info("坐标转换器初始化完成")
    
    def ras_to_lps(self, point: Point3D) -> Point3D:
        """
        将RAS坐标转换为LPS坐标
        
        转换规则：
        - RAS的X（右）→ LPS的-X（左）
        - RAS的Y（前）→ LPS的-Y（后）
        - RAS的Z（上）→ LPS的Z（上）
        
        Args:
            point: RAS坐标系中的点
            
        Returns:
            LPS坐标系中的点
        """
        return Point3D(-point.x, -point.y, point.z)
    
    def lps_to_ras(self, point: Point3D) -> Point3D:
        """
        将LPS坐标转换为RAS坐标
        
        Args:
            point: LPS坐标系中的点
            
        Returns:
            RAS坐标系中的点
        """
        return Point3D(-point.x, -point.y, point.z)
    
    def ras_vector_to_lps(self, vector: Vector3D) -> Vector3D:
        """
        将RAS坐标系中的向量转换为LPS坐标系
        
        Args:
            vector: RAS坐标系中的向量
            
        Returns:
            LPS坐标系中的向量
        """
        return Vector3D(-vector.x, -vector.y, vector.z)
    
    def lps_vector_to_ras(self, vector: Vector3D) -> Vector3D:
        """
        将LPS坐标系中的向量转换为RAS坐标系
        
        Args:
            vector: LPS坐标系中的向量
            
        Returns:
            RAS坐标系中的向量
        """
        return Vector3D(-vector.x, -vector.y, vector.z)
    
    def convert_screw_coordinates(self, coordinates: Dict[str, Point3D], 
                                 from_system: str = "RAS", 
                                 to_system: str = "LPS") -> Dict[str, Point3D]:
        """
        批量转换螺钉坐标
        
        Args:
            coordinates: 螺钉坐标字典
            from_system: 源坐标系 ("RAS" 或 "LPS")
            to_system: 目标坐标系 ("RAS" 或 "LPS")
            
        Returns:
            转换后的坐标字典
        """
        if from_system == to_system:
            logging.info(f"源坐标系和目标坐标系相同({from_system})，无需转换")
            return coordinates.copy()
        
        converted_coordinates = {}
        
        if from_system == "RAS" and to_system == "LPS":
            for name, coord in coordinates.items():
                converted_coord = self.ras_to_lps(coord)
                converted_coordinates[name] = converted_coord
                logging.info(f"螺钉 {name}: RAS({coord.x:.3f}, {coord.y:.3f}, {coord.z:.3f}) "
                           f"→ LPS({converted_coord.x:.3f}, {converted_coord.y:.3f}, {converted_coord.z:.3f})")
        
        elif from_system == "LPS" and to_system == "RAS":
            for name, coord in coordinates.items():
                converted_coord = self.lps_to_ras(coord)
                converted_coordinates[name] = converted_coord
                logging.info(f"螺钉 {name}: LPS({coord.x:.3f}, {coord.y:.3f}, {coord.z:.3f}) "
                           f"→ RAS({converted_coord.x:.3f}, {converted_coord.y:.3f}, {converted_coord.z:.3f})")
        
        else:
            raise ValueError(f"不支持的坐标系转换: {from_system} → {to_system}")
        
        logging.info(f"坐标系转换完成: {from_system} → {to_system}")
        return converted_coordinates
    
    def validate_coordinate_system(self, coordinates: Dict[str, Point3D], 
                                  expected_system: str = "LPS") -> bool:
        """
        验证坐标是否符合预期的坐标系
        
        Args:
            coordinates: 螺钉坐标字典
            expected_system: 预期的坐标系
            
        Returns:
            是否符合预期坐标系
        """
        # 这里可以添加一些启发式规则来判断坐标系
        # 例如，对于肩胛骨手术，在LPS坐标系中：
        # - X坐标通常为负值（左侧）
        # - Y坐标可能为正值（后方）
        # - Z坐标通常为正值（上方）
        
        if expected_system == "LPS":
            # 检查是否大部分X坐标为负值（左侧）
            x_coords = [coord.x for coord in coordinates.values()]
            negative_x_ratio = sum(1 for x in x_coords if x < 0) / len(x_coords)
            
            if negative_x_ratio < 0.5:
                logging.warning(f"坐标可能不是LPS系统：{negative_x_ratio:.1%}的X坐标为负值")
                return False
        
        elif expected_system == "RAS":
            # 检查是否大部分X坐标为正值（右侧）
            x_coords = [coord.x for coord in coordinates.values()]
            positive_x_ratio = sum(1 for x in x_coords if x > 0) / len(x_coords)
            
            if positive_x_ratio < 0.5:
                logging.warning(f"坐标可能不是RAS系统：{positive_x_ratio:.1%}的X坐标为正值")
                return False
        
        logging.info(f"坐标验证通过，符合{expected_system}坐标系")
        return True
    
    def auto_detect_coordinate_system(self, coordinates: Dict[str, Point3D]) -> str:
        """
        自动检测坐标系类型
        
        Args:
            coordinates: 螺钉坐标字典
            
        Returns:
            检测到的坐标系类型 ("RAS" 或 "LPS")
        """
        x_coords = [coord.x for coord in coordinates.values()]
        positive_x_ratio = sum(1 for x in x_coords if x > 0) / len(x_coords)
        
        if positive_x_ratio > 0.5:
            detected_system = "RAS"
        else:
            detected_system = "LPS"
        
        logging.info(f"自动检测坐标系: {detected_system} (正X坐标比例: {positive_x_ratio:.1%})")
        return detected_system


def load_ras_coordinates_from_json(json_file: str) -> Dict[str, Point3D]:
    """
    从JSON文件加载RAS坐标系的螺钉坐标
    
    Args:
        json_file: JSON文件路径
        
    Returns:
        螺钉坐标字典
    """
    import json
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    coordinates = {}
    
    # 支持多种JSON格式
    if 'screw_coordinates_implant_center' in data:
        # 原始格式
        mapping = {
            'center': 'screw_coordinates_implant_center',
            'top': 'screw_coordinates_implant_top',
            'bottom': 'screw_coordinates_implant_bottom'
        }
    else:
        # 简化格式
        mapping = {
            'center': 'center',
            'top': 'top',
            'bottom': 'bottom'
        }
    
    for key, json_key in mapping.items():
        if json_key in data:
            coord_data = data[json_key]
            coordinates[key] = Point3D(coord_data['x'], coord_data['y'], coord_data['z'])
        else:
            raise ValueError(f"JSON文件中缺少坐标: {json_key}")
    
    logging.info(f"从{json_file}加载了{len(coordinates)}个RAS坐标")
    return coordinates


def convert_and_save_coordinates(input_file: str, output_file: str, 
                               from_system: str = "RAS", to_system: str = "LPS"):
    """
    转换坐标系并保存到新文件
    
    Args:
        input_file: 输入JSON文件
        output_file: 输出JSON文件
        from_system: 源坐标系
        to_system: 目标坐标系
    """
    import json
    
    # 加载坐标
    if from_system == "RAS":
        coordinates = load_ras_coordinates_from_json(input_file)
    else:
        raise NotImplementedError(f"暂不支持从{from_system}坐标系加载")
    
    # 转换坐标系
    transformer = CoordinateTransformer()
    converted_coordinates = transformer.convert_screw_coordinates(
        coordinates, from_system, to_system)
    
    # 保存转换后的坐标
    output_data = {}
    for name, coord in converted_coordinates.items():
        output_data[name] = {
            'x': coord.x,
            'y': coord.y,
            'z': coord.z
        }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    logging.info(f"转换后的{to_system}坐标已保存到: {output_file}")


if __name__ == "__main__":
    # 测试坐标转换
    logging.basicConfig(level=logging.INFO)
    
    # 示例RAS坐标
    ras_coords = {
        'center': Point3D(70.0379, -195.678, 215.651),
        'top': Point3D(69.6298, -190.934, 222.572),
        'bottom': Point3D(70.446, -200.422, 208.731)
    }
    
    transformer = CoordinateTransformer()
    
    # 转换为LPS
    lps_coords = transformer.convert_screw_coordinates(ras_coords, "RAS", "LPS")
    
    print("RAS → LPS 转换结果:")
    for name, coord in lps_coords.items():
        print(f"  {name}: ({coord.x:.3f}, {coord.y:.3f}, {coord.z:.3f})")
