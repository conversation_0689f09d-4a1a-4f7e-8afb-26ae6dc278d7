"""
业务螺钉路径规划命令行接口

提供命令行工具来执行基于业务需求的螺钉路径规划
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime
from typing import Dict, Any

# 添加src目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)
sys.path.insert(0, src_dir)

try:
    from core.business_planning import BusinessPathPlanner, BusinessPlanningInput, ScrewSpec
    from utils.geometry import Point3D
    from utils.io_utils import DataLoader
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在ScrewPlanning目录下运行此脚本")
    sys.exit(1)


def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    # 创建logs目录
    project_root = os.path.dirname(src_dir)
    log_dir = os.path.join(project_root, 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f'business_planning_{datetime.now().strftime("%Y%m%d")}.log')
    
    level = getattr(logging, log_level.upper(), logging.INFO)
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def load_screw_coordinates(coord_file: str) -> Dict[str, Point3D]:
    """
    从JSON文件加载螺钉坐标
    
    期望格式:
    {
        "center": {"x": 70.0, "y": -195.6, "z": 215.6},
        "top": {"x": 69.6, "y": -190.9, "z": 222.5},
        "bottom": {"x": 70.4, "y": -200.4, "z": 208.7}
    }
    """
    try:
        with open(coord_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        coordinates = {}
        possible_keys = ['center', 'top', 'bottom']

        for key in possible_keys:
            if key in data:
                coord = data[key]
                coordinates[key] = Point3D(coord['x'], coord['y'], coord['z'])
            else:
                raise ValueError(f"JSON文件中缺少坐标: {key}")

        # 至少需要一个螺钉坐标
        if not coordinates:
            raise ValueError("至少需要一个螺钉坐标")
        
        return coordinates
        
    except Exception as e:
        raise ValueError(f"加载螺钉坐标失败: {e}")


def create_default_screw_specs() -> Dict[str, ScrewSpec]:
    """创建默认螺钉规格"""
    return {
        'center': ScrewSpec(length=30.0, radius=3.25),  # 6.5mm直径，30mm长度
        'top': ScrewSpec(length=30.0, radius=3.25),
        'bottom': ScrewSpec(length=30.0, radius=3.25)
    }


def load_screw_specs(spec_file: str = None) -> Dict[str, ScrewSpec]:
    """
    从JSON文件加载螺钉规格，如果文件不存在则使用默认值
    
    期望格式:
    {
        "center": {"length": 50.0, "radius": 3.25},
        "top": {"length": 45.0, "radius": 3.0},
        "bottom": {"length": 55.0, "radius": 3.5}
    }
    """
    if spec_file is None or not os.path.exists(spec_file):
        logging.info("使用默认螺钉规格")
        return create_default_screw_specs()
    
    try:
        with open(spec_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        specs = {}
        for key in ['center', 'top', 'bottom']:
            if key in data:
                spec_data = data[key]
                specs[key] = ScrewSpec(
                    length=spec_data['length'],
                    radius=spec_data['radius']
                )
            else:
                raise ValueError(f"JSON文件中缺少螺钉规格: {key}")
            
        return specs
        
    except Exception as e:
        logging.warning(f"加载螺钉规格失败: {e}，使用默认值")
        return create_default_screw_specs()


def save_results(result, output_file: str):
    """保存规划结果到JSON文件"""
    output_data = {
        "success": result.success,
        "planning_time": result.planning_time,
        "error_message": result.error_message,
        "screw_endpoints": {},
        "statistics": {}
    }

    # 转换统计信息，处理Vector3D对象
    if result.statistics:
        for key, value in result.statistics.items():
            if hasattr(value, 'x') and hasattr(value, 'y') and hasattr(value, 'z'):
                # Vector3D或Point3D对象
                output_data["statistics"][key] = {
                    "x": value.x, "y": value.y, "z": value.z
                }
            else:
                output_data["statistics"][key] = value
    
    # 转换螺钉末端坐标
    for screw_name, endpoint in result.screw_endpoints.items():
        output_data["screw_endpoints"][screw_name] = {
            "x": endpoint.x,
            "y": endpoint.y,
            "z": endpoint.z
        }
    
    # 添加详细路径信息（可选）
    if result.screw_paths:
        output_data["screw_paths"] = {}
        for screw_name, path in result.screw_paths.items():
            output_data["screw_paths"][screw_name] = {
                "start_point": {"x": path.start_point.x, "y": path.start_point.y, "z": path.start_point.z},
                "end_point": {"x": path.end_point.x, "y": path.end_point.y, "z": path.end_point.z},
                "length": path.length,
                "radius": path.radius,
                "bone_density_integral": path.bone_density_integral,
                "is_valid": path.is_valid,
                "coverage_ratio": path.coverage_ratio
            }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)


def print_results(result):
    """打印规划结果到控制台"""
    print("\n" + "=" * 60)
    print("螺钉路径规划结果")
    print("=" * 60)
    
    if result.success:
        print("✅ 规划成功！")
        print(f"📊 规划耗时: {result.planning_time:.2f} 秒")
        print(f"🎯 成功规划螺钉数量: {len(result.screw_endpoints)}")
        
        print("\n📍 螺钉末端坐标:")
        for screw_name, endpoint in result.screw_endpoints.items():
            print(f"  {screw_name:>6}: ({endpoint.x:>8.2f}, {endpoint.y:>8.2f}, {endpoint.z:>8.2f})")
        
        # 显示Pareto解信息（如果有）
        if hasattr(result, 'pareto_solutions') and result.pareto_solutions:
            print(f"\n🎯 Pareto最优解信息:")
            for screw_name, solutions in result.pareto_solutions.items():
                print(f"  {screw_name} 螺钉:")
                for i, solution in enumerate(solutions[:3]):  # 只显示前3个解
                    print(f"    解{i+1}: Rank={solution.rank}, 拥挤距离={solution.crowding_distance:.3f}")
                    print(f"         {solution.objectives}")
                if len(solutions) > 3:
                    print(f"    ... 还有 {len(solutions) - 3} 个解")

        if result.statistics:
            print(f"\n📈 统计信息:")
            stats = result.statistics
            if 'base_normal' in stats:
                normal = stats['base_normal']
                print(f"  基座法向量: ({normal.x:.3f}, {normal.y:.3f}, {normal.z:.3f})")
            if 'base_center' in stats:
                center = stats['base_center']
                print(f"  基座中心: ({center.x:.2f}, {center.y:.2f}, {center.z:.2f})")
    else:
        print("❌ 规划失败")
        print(f"错误信息: {result.error_message}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="业务螺钉路径规划工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
    使用示例:
    python run_business_planning.py --ct examples/PlanData/CT.nii.gz /
    --mask examples/PlanData/scapula_mask.nii.gz /
    --coords examples/PlanData/screw_coords_top_bottom.json /
    --specs examples/PlanData/screw_specs.json --screws top bottom --constraint-angle 10.0/
    --save-paths --output top_bottom_result_with_paths.json

    坐标文件格式 (screw_coords.json):
    {
    "center": {"x": 70.0, "y": -195.6, "z": 215.6},
    "top": {"x": 69.6, "y": -190.9, "z": 222.5},
    "bottom": {"x": 70.4, "y": -200.4, "z": 208.7}
    }

    规格文件格式 (screw_specs.json, 可选):
    {
    "center": {"length": 50.0, "radius": 3.25},
    "top": {"length": 50.0, "radius": 3.25},
    "bottom": {"length": 50.0, "radius": 3.25}
    }
        """
    )
    
    # 必需参数
    parser.add_argument('--ct', required=True, help='CT图像文件路径 (.nii.gz)')
    parser.add_argument('--mask', required=True, help='肩胛骨掩膜文件路径 (.nii.gz)')
    parser.add_argument('--coords', required=True, help='螺钉坐标文件路径 (.json)')
    
    # 可选参数
    parser.add_argument('--specs', help='螺钉规格文件路径 (.json, 可选)')
    parser.add_argument('--output', help='输出结果文件路径 (.json, 可选)')
    parser.add_argument('--constraint-angle', type=float, default=15.0, help='约束角度 (度, 默认: 15.0)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='日志级别 (默认: INFO)')
    
    # 算法参数
    parser.add_argument('--radial-resolution', type=int, default=30, help='径向分辨率 (默认: 30)')
    parser.add_argument('--circumferential-resolution', type=int, default=150, help='周向分辨率 (默认: 150)')
    parser.add_argument('--integration-resolution', type=int, default=30, help='积分分辨率 (默认: 30)')

    # 螺钉选择参数
    parser.add_argument('--screws', nargs='+', choices=['center', 'top', 'bottom'],
                       help='要规划的螺钉 (可选: center, top, bottom)。不指定则规划所有可用螺钉')

    # 优化方法参数
    parser.add_argument('--optimization-method', choices=['weighted', 'pareto'], default='weighted',
                       help='优化方法 (默认: weighted)。weighted=加权综合评分，pareto=Pareto最优化')
    parser.add_argument('--max-pareto-solutions', type=int, default=5,
                       help='Pareto优化时最大返回解数量 (默认: 5)')

    # 路径可视化参数
    parser.add_argument('--save-paths', action='store_true',
                       help='保存所有筛选路径的三维二值图 (生成 {screw_name}.nii.gz 文件)')
    
    args = parser.parse_args()
    setup_logging(args.log_level)
    
    try:
        # 检查输入文件
        for file_path, name in [(args.ct, 'CT图像'), (args.mask, '肩胛骨掩膜'), (args.coords, '螺钉坐标')]:
            if not os.path.exists(file_path):
                print(f"❌ {name}文件不存在: {file_path}")
                sys.exit(1)
        
        print("✅ 输入文件检查通过")
        
        # 加载数据
        print("📂 加载数据...")
        loader = DataLoader()
        ct_image = loader.load_ct_image(args.ct)
        mask_image = loader.load_mask_image(args.mask)
        screw_coordinates = load_screw_coordinates(args.coords)
        screw_specs = load_screw_specs(args.specs)

        print("✅ 数据加载成功")
        print(f"📍 螺钉坐标: {len(screw_coordinates)} 个")
        print(f"🔧 螺钉规格: {len(screw_specs)} 个")

        # 显示加载的螺钉规格
        print("\n🔧 螺钉规格详情:")
        for name, spec in screw_specs.items():
            print(f"  {name:>6}: 长度={spec.length:>5.1f}mm, 半径={spec.radius:>4.2f}mm, 直径={spec.diameter:>4.1f}mm")
        
        print("✅ 数据加载成功")
        print(f"📍 螺钉坐标: {len(screw_coordinates)} 个")
        print(f"🔧 螺钉规格: {len(screw_specs)} 个")
        
        # 创建输入参数
        input_params = BusinessPlanningInput(
            ct_image=ct_image,
            mask_image=mask_image,
            screw_coordinates=screw_coordinates,
            screw_specs=screw_specs,
            constraint_angle=args.constraint_angle,
            enabled_screws=args.screws,
            save_paths=args.save_paths,
            radial_resolution=args.radial_resolution,
            circumferential_resolution=args.circumferential_resolution,
            integration_resolution=args.integration_resolution,
            optimization_method=args.optimization_method,
            max_pareto_solutions=args.max_pareto_solutions
        )
        
        print("🚀 开始执行路径规划...")
        planner = BusinessPathPlanner()
        result = planner.plan_screws(input_params)
        
        # 显示结果
        print_results(result)

        # 保存结果
        if args.output:
            save_results(result, args.output)
            print(f"\n💾 结果已保存到: {args.output}")
        
        if result.success:
            print("\n🎉 规划完成！")
            sys.exit(0)
        else:
            print("\n❌ 规划失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 执行过程中出错: {e}")
        logging.exception("详细错误信息:")
        sys.exit(1)


if __name__ == "__main__":
    main()
