# UI界面移除说明

## 📢 重要通知

根据用户需求，**图形用户界面（UI）已从本项目中完全移除**。

## 🗑️ 已删除的内容

### 代码文件
- `src/ui/` - 整个UI模块目录
  - `src/ui/main_window.py` - 主窗口界面
  - `src/ui/planning_widget.py` - 规划控制面板
  - `src/ui/visualization.py` - VTK 3D可视化组件
  - `src/ui/__init__.py` - UI模块初始化文件

### 修改的文件
- `src/main.py` - 改为命令行工具，显示使用说明
- `run_screw_planning.py` - 更新为显示使用说明而非启动UI

### 更新的文档
- `README.md` - 移除UI相关描述，更新使用方法
- `USAGE_GUIDE.md` - 移除图形界面操作说明
- 其他文档中的UI引用已相应更新

## ✅ 保留的功能

### 核心算法模块（完全保留）
- `src/core/bone_density.py` - 骨密度计算
- `src/core/cone_space.py` - 锥形空间生成
- `src/core/business_planning.py` - 业务路径规划算法
- `src/core/optimization.py` - 路径优化

### 工具模块（完全保留）
- `src/utils/geometry.py` - 几何计算工具
- `src/utils/image_processing.py` - 图像处理工具
- `src/utils/io_utils.py` - 文件输入输出工具

### 数据和文档（完全保留）
- `examples/PlanData/` - 示例数据
- `docs/` - 完整技术文档
- `tests/` - 测试文件

## 🚀 如何使用系统

### 1. 查看使用说明
```bash
python run_screw_planning.py
```

### 2. 使用编程接口
```python
import sys
sys.path.append('src')

from core.business_planning import BusinessPathPlanner, BusinessPlanningInput, ScrewSpec
from utils.io_utils import DataLoader
from utils.geometry import Point3D

# 加载数据
loader = DataLoader()
ct_image = loader.load_ct_image('examples/PlanData/CT.nii.gz')
mask_image = loader.load_mask_image('examples/PlanData/scapula_mask.nii.gz')

# 定义螺钉坐标和规格
screw_coordinates = {
    'center': Point3D(-62.242, 198.690, 217.256),
    'top': Point3D(-68.343, 191.373, 222.918),
    'bottom': Point3D(-69.168, 200.974, 208.913)
}
screw_specs = {
    'center': ScrewSpec(length=25.0, radius=3.25),
    'top': ScrewSpec(length=25.0, radius=2.38),
    'bottom': ScrewSpec(length=25.0, radius=2.38)
}

# 创建输入参数并执行规划
input_params = BusinessPlanningInput(
    ct_image=ct_image, mask_image=mask_image,
    screw_coordinates=screw_coordinates, screw_specs=screw_specs
)
planner = BusinessPathPlanner()
result = planner.plan_screws(input_params)

# 查看结果
if result.success:
    print(f"规划成功！规划时间: {result.planning_time:.2f}秒")
    for screw_name, endpoint in result.screw_endpoints.items():
        print(f"{screw_name} 螺钉终点: ({endpoint.x:.2f}, {endpoint.y:.2f}, {endpoint.z:.2f})")
        print(f"螺钉 {i+1}: 骨密度积分 = {path.bone_density_integral:.2f}")
```

### 3. 运行示例脚本
```bash
python examples/basic_planning_example.py
```

## 📚 相关文档

- **README.md** - 项目概述和快速开始
- **USAGE_GUIDE.md** - 详细使用指南
- **docs/user_manual.md** - 用户手册
- **docs/algorithm_design.md** - 算法设计文档

## 💡 优势

移除UI界面后，系统具有以下优势：

1. **更轻量级** - 减少了PyQt5和VTK等UI依赖
2. **更易集成** - 纯Python API便于集成到其他系统
3. **更高性能** - 无UI渲染开销，计算更快
4. **更好维护** - 专注于核心算法，代码更简洁
5. **更灵活** - 可以轻松集成到Web应用、批处理脚本等

## 🔧 技术支持

如果您需要：
- 重新添加UI界面
- 集成到现有系统
- 自定义功能开发
- 技术咨询

请参考项目文档或联系开发团队。

---

**注意**: 所有核心功能和算法完全保留，只是移除了图形界面。系统的科学计算能力和准确性没有任何改变。
