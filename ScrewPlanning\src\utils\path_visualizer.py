#!/usr/bin/env python3
"""
路径可视化工具

生成螺钉路径的三维二值图像，用于在Slicer中可视化
"""

import os
import numpy as np
import SimpleITK as sitk
import logging
from typing import List, Tuple, Dict, Any
from utils.geometry import Point3D, Vector3D


class PathVisualizer:
    """路径可视化器"""
    
    def __init__(self, ct_image: sitk.Image):
        """
        初始化路径可视化器
        
        Args:
            ct_image: CT图像，用于获取空间信息
        """
        self.ct_image = ct_image
        self.size = ct_image.GetSize()
        self.origin = ct_image.GetOrigin()
        self.spacing = ct_image.GetSpacing()
        self.direction = ct_image.GetDirection()
        
        logging.info(f"路径可视化器初始化完成，图像尺寸: {self.size}")
    
    def create_path_volume(self, paths: List[Tuple[Point3D, Point3D]],
                          screw_radius: float = 1.0) -> np.ndarray:
        """
        创建路径的三维二值体积

        Args:
            paths: 路径列表，每个路径包含起始点和终点
            screw_radius: 可视化半径（mm），建议使用较小值如1.0mm以避免过度膨胀

        Returns:
            三维二值数组
        """
        # 创建空的二值体积
        volume = np.zeros(self.size[::-1], dtype=np.uint8)  # ITK使用 (z,y,x) 顺序
        
        logging.info(f"创建路径体积，路径数量: {len(paths)}")
        
        for i, (start_point, end_point) in enumerate(paths):
            if i % 100 == 0:
                logging.info(f"处理路径进度: {i}/{len(paths)}")
            
            # 在路径上采样点
            self._draw_path_in_volume(volume, start_point, end_point, screw_radius)
        
        logging.info(f"路径体积创建完成，非零体素数量: {np.sum(volume > 0)}")
        return volume
    
    def _draw_path_in_volume(self, volume: np.ndarray, 
                           start_point: Point3D, end_point: Point3D, 
                           radius: float):
        """
        在体积中绘制单条路径
        
        Args:
            volume: 三维体积数组
            start_point: 起始点
            end_point: 终点
            radius: 螺钉半径
        """
        # 计算路径方向和长度
        direction = Vector3D(
            end_point.x - start_point.x,
            end_point.y - start_point.y,
            end_point.z - start_point.z
        )
        length = direction.magnitude()
        
        if length == 0:
            return
        
        direction = direction.normalize()
        
        # 沿路径采样
        num_samples = max(int(length / min(self.spacing)), 10)
        
        for i in range(num_samples + 1):
            t = i / num_samples
            
            # 当前采样点
            current_point = Point3D(
                start_point.x + t * (end_point.x - start_point.x),
                start_point.y + t * (end_point.y - start_point.y),
                start_point.z + t * (end_point.z - start_point.z)
            )
            
            # 转换为体素坐标
            try:
                voxel_coords = self.ct_image.TransformPhysicalPointToIndex([
                    current_point.x, current_point.y, current_point.z
                ])
                
                # 检查是否在图像范围内
                if (0 <= voxel_coords[0] < self.size[0] and
                    0 <= voxel_coords[1] < self.size[1] and
                    0 <= voxel_coords[2] < self.size[2]):
                    
                    # 绘制螺钉横截面（圆形）
                    self._draw_circle_at_voxel(volume, voxel_coords, radius)
                    
            except Exception:
                # 点在图像外，跳过
                continue
    
    def _draw_circle_at_voxel(self, volume: np.ndarray, 
                            center_voxel: Tuple[int, int, int], 
                            radius_mm: float):
        """
        在指定体素位置绘制圆形横截面
        
        Args:
            volume: 三维体积数组
            center_voxel: 中心体素坐标 (x, y, z)
            radius_mm: 半径（毫米）
        """
        # 转换半径到体素单位
        radius_voxels = [
            radius_mm / self.spacing[0],  # x方向
            radius_mm / self.spacing[1],  # y方向
            radius_mm / self.spacing[2]   # z方向
        ]
        
        # 计算搜索范围
        search_range = [
            int(np.ceil(radius_voxels[0])) + 1,
            int(np.ceil(radius_voxels[1])) + 1,
            int(np.ceil(radius_voxels[2])) + 1
        ]
        
        cx, cy, cz = center_voxel
        
        # 在搜索范围内查找圆形区域
        for dx in range(-search_range[0], search_range[0] + 1):
            for dy in range(-search_range[1], search_range[1] + 1):
                for dz in range(-search_range[2], search_range[2] + 1):
                    
                    x, y, z = cx + dx, cy + dy, cz + dz
                    
                    # 检查边界
                    if (0 <= x < self.size[0] and
                        0 <= y < self.size[1] and
                        0 <= z < self.size[2]):
                        
                        # 计算距离（考虑各向异性体素）
                        distance_squared = (
                            (dx / radius_voxels[0]) ** 2 +
                            (dy / radius_voxels[1]) ** 2 +
                            (dz / radius_voxels[2]) ** 2
                        )
                        
                        # 如果在圆形内，设置为1
                        if distance_squared <= 1.0:
                            volume[z, y, x] = 1  # ITK使用 (z,y,x) 顺序
    
    def save_paths_as_nifti(self, paths_dict: Dict[str, List[Tuple[Point3D, Point3D]]],
                           output_dir: str = ".",
                           visualization_radius: float = 1.0):
        """
        保存路径为NIfTI文件

        Args:
            paths_dict: 路径字典，键为螺钉名称，值为路径列表
            output_dir: 输出目录
            visualization_radius: 可视化半径（mm），建议1.0mm以获得清晰的路径显示
        """
        os.makedirs(output_dir, exist_ok=True)
        
        for screw_name, paths in paths_dict.items():
            if not paths:
                logging.warning(f"螺钉 {screw_name} 没有路径数据")
                continue
            
            logging.info(f"保存螺钉 {screw_name} 的路径，数量: {len(paths)}")
            
            # 创建路径体积
            volume = self.create_path_volume(paths, visualization_radius)
            
            # 创建ITK图像
            itk_image = sitk.GetImageFromArray(volume)
            itk_image.SetOrigin(self.origin)
            itk_image.SetSpacing(self.spacing)
            itk_image.SetDirection(self.direction)
            
            # 保存文件
            output_file = os.path.join(output_dir, f"{screw_name}.nii.gz")
            sitk.WriteImage(itk_image, output_file)
            
            logging.info(f"路径可视化已保存: {output_file}")
            print(f"✅ 已保存 {screw_name} 螺钉路径可视化: {output_file}")


def create_path_visualization(ct_image: sitk.Image,
                            paths_dict: Dict[str, List[Tuple[Point3D, Point3D]]],
                            output_dir: str = ".",
                            visualization_radius: float = 1.0):
    """
    创建路径可视化的便捷函数

    Args:
        ct_image: CT图像
        paths_dict: 路径字典
        output_dir: 输出目录
        visualization_radius: 可视化半径（mm）
    """
    visualizer = PathVisualizer(ct_image)
    visualizer.save_paths_as_nifti(paths_dict, output_dir, visualization_radius)
