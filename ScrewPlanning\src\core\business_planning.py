"""
业务螺钉路径规划模块

基于实际业务需求的螺钉路径规划系统，支持：
- 3个螺钉起始坐标输入（中心、上、下）
- 自动方向判断
- 独立螺钉路径规划
- 螺钉末端坐标输出
"""

import math
import logging
from typing import List, Tuple, Optional, Dict, Any, Union
import os
from dataclasses import dataclass
import SimpleITK as sitk

try:
    from ..utils.geometry import Point3D, Vector3D, ScrewPath, PlanningResult
    from ..utils.coordinate_transform import CoordinateTransformer
    from .bone_density import BoneDensityCalculator
    from .cone_space import ConeSpaceGenerator
except ImportError:
    from utils.geometry import Point3D, Vector3D, ScrewPath, PlanningResult
    from utils.coordinate_transform import CoordinateTransformer
    from core.bone_density import BoneDensityCalculator
    from core.cone_space import ConeSpaceGenerator


@dataclass
class ScrewSpec:
    """螺钉规格"""
    length: float  # 长度 (mm)
    radius: float  # 半径 (mm)
    
    @property
    def diameter(self) -> float:
        """直径 (mm)"""
        return self.radius * 2


@dataclass
class BusinessPlanningInput:
    """业务规划输入参数"""
    # 必需参数
    ct_image: sitk.Image  # CT图像
    mask_image: sitk.Image  # 肩胛骨掩膜
    screw_coordinates: Dict[str, Point3D]  # 螺钉起始坐标
    screw_specs: Dict[str, ScrewSpec]  # 螺钉规格

    # 可选参数
    constraint_angle: float = 15.0  # 约束角度（度）
    coordinate_system: str = "LPS"  # 坐标系 ("RAS" 或 "LPS")
    enabled_screws: Optional[List[str]] = None  # 要规划的螺钉列表，None表示全部
    save_paths: bool = False  # 是否保存路径可视化

    # 算法参数
    radial_resolution: int = 30
    circumferential_resolution: int = 150
    integration_resolution: int = 30

    # 优化参数
    optimization_method: str = "weighted"  # 优化方法: "weighted" 或 "pareto"
    max_pareto_solutions: int = 5  # Pareto优化时最大返回解数量
    
    def validate(self) -> bool:
        """验证输入参数"""
        # 确定需要检查的螺钉
        if self.enabled_screws is not None:
            required_screws = self.enabled_screws
        else:
            required_screws = list(self.screw_coordinates.keys())

        # 至少需要一个螺钉
        if not required_screws:
            logging.error("至少需要一个螺钉")
            return False

        # 检查螺钉坐标
        for screw_name in required_screws:
            if screw_name not in self.screw_coordinates:
                logging.error(f"缺少螺钉坐标: {screw_name}")
                return False

        # 检查螺钉规格
        for screw_name in required_screws:
            if screw_name not in self.screw_specs:
                logging.error(f"缺少螺钉规格: {screw_name}")
                return False

            spec = self.screw_specs[screw_name]
            if spec.length <= 0 or spec.radius <= 0:
                logging.error(f"螺钉规格无效: {screw_name}")
                return False

        return True


@dataclass
class BusinessPlanningResult:
    """业务规划结果"""
    success: bool = False
    error_message: str = ""
    planning_time: float = 0.0
    
    # 螺钉末端坐标
    screw_endpoints: Dict[str, Point3D] = None
    
    # 详细路径信息（可选）
    screw_paths: Dict[str, ScrewPath] = None

    # Pareto最优解（当使用Pareto优化时）
    pareto_solutions: Dict[str, List] = None  # 每个螺钉的Pareto最优解列表

    # 统计信息
    statistics: Dict[str, Any] = None

    # 所有筛选路径（用于可视化）
    all_paths: Dict[str, List[Tuple[Point3D, Point3D]]] = None
    
    def __post_init__(self):
        if self.screw_endpoints is None:
            self.screw_endpoints = {}
        if self.screw_paths is None:
            self.screw_paths = {}
        if self.statistics is None:
            self.statistics = {}
        if self.all_paths is None:
            self.all_paths = {}


class DirectionAnalyzer:
    """方向分析器 - 判断螺钉植入方向"""
    
    def __init__(self, ct_image: sitk.Image, mask_image: sitk.Image):
        self.ct_image = ct_image
        self.mask_image = mask_image
        self.bone_density_calculator = BoneDensityCalculator(ct_image, mask_image)
    
    def analyze_implant_direction(self, screw_coordinates: Dict[str, Point3D]) -> Tuple[Vector3D, Point3D]:
        """
        分析基座植入方向

        Args:
            screw_coordinates: 螺钉坐标字典，可能包含 center, top, bottom 中的任意组合

        Returns:
            (基座法向量, 基座中心点)
        """
        available_screws = list(screw_coordinates.keys())
        logging.info(f"可用螺钉: {available_screws}")

        if 'center' in screw_coordinates and 'top' in screw_coordinates and 'bottom' in screw_coordinates:
            center = screw_coordinates['center']
            top = screw_coordinates['top']
            bottom = screw_coordinates['bottom']
            logging.info(f"使用改进三点方法 - 中心: {center}, 上: {top}, 下: {bottom}")

            # 步骤1：计算三个点形成的三角平面的法向量
            vector1 = top - center
            vector2 = bottom - center
            triangle_normal = vector1.cross(vector2).normalize()  # 将长度变为1，只保留方向信息
            logging.info(f"三角平面法向量: {triangle_normal}")

            # 步骤2：计算上下螺钉连线向量
            screw_line_vector = top - bottom  # 这条线代表基座边缘的方向，基座平面必须包含这条线。

            # 步骤3：计算基座平面法向量（垂直于三角平面，也垂直于上下螺钉连线）
            normal = triangle_normal.cross(screw_line_vector).normalize()

            # 步骤4：基座中心为上下螺钉的中点（因为基座平面过上下螺钉）
            base_center = Point3D(
                (top.x + bottom.x) / 2,
                (top.y + bottom.y) / 2,
                (top.z + bottom.z) / 2
            )

            logging.info(f"基座平面法向量: {normal}, 基座中心: {base_center}")

        elif 'top' in screw_coordinates and 'bottom' in screw_coordinates:
            raise NotImplementedError("两点方法已废弃")
            # 两点方法：使用上下螺钉
            # top = screw_coordinates['top']
            # bottom = screw_coordinates['bottom']

            # logging.info(f"使用两点方法 - 上: {top}, 下: {bottom}")

            # # 基座中心为上下螺钉的中点
            # base_center = Point3D(
            #     (top.x + bottom.x) / 2,
            #     (top.y + bottom.y) / 2,
            #     (top.z + bottom.z) / 2
            # )

            # # 计算上下向量
            # up_down_vector = top - bottom

            # # 假设一个垂直方向作为第二个向量（可以根据实际情况调整）
            # # 这里使用Z轴方向作为参考
            # reference_vector = Vector3D(0, 0, 1)

            # # 如果上下向量与参考向量平行，使用Y轴方向
            # if abs(up_down_vector.normalize().dot(reference_vector)) > 0.9:
            #     reference_vector = Vector3D(0, 1, 0)

            # # 计算法向量
            # normal = up_down_vector.cross(reference_vector).normalize()

        else:
            raise ValueError(f"无法从可用螺钉 {available_screws} 计算基座方向，至少需要 center, top 和 bottom 螺钉")

        # 判断法向量方向 - 应该指向肩盂内
        corrected_normal = self._correct_normal_direction(base_center, normal)

        logging.info(f"基座法向量: {corrected_normal}, 基座中心: {base_center}")
        return corrected_normal, base_center
    
    def _correct_normal_direction(self, center: Point3D, normal: Vector3D) -> Vector3D:
        """
        修正法向量方向，确保指向肩盂内

        通过检查哪个方向能进入肩胛骨掩膜来判断正确方向
        """
        # 测试距离
        test_distance = 20.0  # mm，增加测试距离

        # 两个候选方向
        direction1 = normal
        direction2 = normal * -1

        # 检查两个方向上是否能进入掩膜
        mask_entry1 = self._check_direction_mask_entry(center, direction1, test_distance)
        mask_entry2 = self._check_direction_mask_entry(center, direction2, test_distance)

        logging.info(f"方向1进入掩膜: {mask_entry1}, 方向2进入掩膜: {mask_entry2}")

        # 选择能进入掩膜的方向
        if mask_entry1 and not mask_entry2:
            return direction1
        elif mask_entry2 and not mask_entry1:
            return direction2
        elif mask_entry1 and mask_entry2:
            # 如果两个方向都能进入掩膜，选择骨密度更高的方向
            density1 = self._sample_direction_density(center, direction1, test_distance)
            density2 = self._sample_direction_density(center, direction2, test_distance)
            logging.info(f"两方向都可进入掩膜，比较骨密度 - 方向1: {density1:.2f}, 方向2: {density2:.2f}")
            return direction1 if density1 > density2 else direction2
        else:
            # 如果都不能进入掩膜，选择骨密度更高的方向作为备选
            density1 = self._sample_direction_density(center, direction1, test_distance)
            density2 = self._sample_direction_density(center, direction2, test_distance)
            logging.warning(f"两方向都无法进入掩膜，使用骨密度判断 - 方向1: {density1:.2f}, 方向2: {density2:.2f}")
            return direction1 if density1 > density2 else direction2
    
    def _sample_direction_density(self, start: Point3D, direction: Vector3D, distance: float) -> float:
        """在指定方向上采样骨密度"""
        sample_count = 5
        total_density = 0.0
        valid_samples = 0

        for i in range(1, sample_count + 1):
            sample_distance = distance * i / sample_count
            sample_point_array = start.to_array() + direction.to_array() * sample_distance
            sample_point = Point3D.from_array(sample_point_array)

            density = self.bone_density_calculator.get_bone_density_at_point(sample_point)
            if density is not None:
                total_density += density
                valid_samples += 1

        return total_density / max(valid_samples, 1)

    def _check_direction_mask_entry(self, start: Point3D, direction: Vector3D, distance: float) -> bool:
        """
        检查指定方向上是否能进入肩胛骨掩膜

        Args:
            start: 起始点
            direction: 方向向量
            distance: 检查距离

        Returns:
            是否能进入掩膜
        """
        sample_count = 10

        for i in range(1, sample_count + 1):
            sample_distance = distance * i / sample_count
            sample_point_array = start.to_array() + direction.to_array() * sample_distance
            sample_point = Point3D.from_array(sample_point_array)

            if self.bone_density_calculator.is_point_in_mask(sample_point):
                return True

        return False

    def diagnose_coordinates(self, screw_coordinates: Dict[str, Point3D]) -> Dict[str, Any]:
        """
        诊断螺钉坐标是否有效

        Args:
            screw_coordinates: 螺钉坐标字典

        Returns:
            诊断结果
        """
        diagnosis = {}

        for screw_name, coord in screw_coordinates.items():
            result = {
                'coordinate': coord,
                'in_mask': self.bone_density_calculator.is_point_in_mask(coord),
                'hu_value': self.bone_density_calculator.get_hu_value_at_point(coord),
                'bone_density': self.bone_density_calculator.get_bone_density_at_point(coord)
            }
            diagnosis[screw_name] = result

            logging.info(f"螺钉 {screw_name} 诊断:")
            logging.info(f"  坐标: {coord}")
            logging.info(f"  在掩膜内: {result['in_mask']}")
            logging.info(f"  HU值: {result['hu_value']}")
            logging.info(f"  骨密度: {result['bone_density']}")

        return diagnosis


class BusinessPathPlanner:
    """业务路径规划器"""
    
    def __init__(self):
        self.direction_analyzer = None
        self.bone_density_calculator = None
        self.cone_space_generator = None
        self.optimizer = None
        self.pareto_optimizer = None
    
    def plan_screws(self, input_params: BusinessPlanningInput) -> BusinessPlanningResult:
        """
        执行螺钉路径规划
        
        Args:
            input_params: 输入参数
            
        Returns:
            规划结果
        """
        import time
        start_time = time.time()
        result = BusinessPlanningResult()
        
        try:
            # 验证输入参数
            if not input_params.validate():
                result.error_message = "输入参数验证失败"
                return result
            
            logging.info("开始业务螺钉路径规划...")

            self._initialize_components(input_params)

            # 诊断螺钉坐标（起始坐标可以在掩膜外，这是正常的）
            diagnosis = self.direction_analyzer.diagnose_coordinates(input_params.screw_coordinates)
            logging.info(f"螺钉坐标诊断完成: {diagnosis}")

            # 分析植入方向
            base_normal, base_center = self.direction_analyzer.analyze_implant_direction(input_params.screw_coordinates)

            if input_params.enabled_screws is not None:
                screw_names = input_params.enabled_screws
            else:
                screw_names = ['center', 'top', 'bottom']  # 默认规划所有可用的螺钉

            available_screws = [name for name in screw_names if name in input_params.screw_coordinates]

            if not available_screws:
                result.error_message = "没有找到可规划的螺钉坐标"
                return result

            logging.info(f"将规划以下螺钉: {available_screws}")

            for screw_name in available_screws:
                logging.info(f"正在规划 {screw_name} 螺钉")
                
                start_point = input_params.screw_coordinates[screw_name]
                screw_spec = input_params.screw_specs[screw_name]
                
                # 计算该螺钉的锥轴方向
                axis_direction = self._calculate_screw_axis(screw_name, start_point, base_normal, base_center)
                
                # 规划单个螺钉路径
                screw_result = self._plan_single_screw(start_point, axis_direction, screw_spec, input_params, screw_name)
                
                if screw_result.success and screw_result.optimal_paths:
                    optimal_path = screw_result.optimal_paths[0]
                    result.screw_endpoints[screw_name] = optimal_path.end_point
                    result.screw_paths[screw_name] = optimal_path

                    # 收集有效路径端点（用于可视化）
                    if hasattr(screw_result, 'valid_path_endpoints'):
                        result.all_paths[screw_name] = screw_result.valid_path_endpoints

                    logging.info(f"螺钉 {screw_name} 规划成功，末端坐标: {optimal_path.end_point}")
                else:
                    result.error_message = f"螺钉 {screw_name} 规划失败: {screw_result.error_message}"
                    return result
            
            result.success = True
            result.planning_time = time.time() - start_time
            
            # 统计信息
            result.statistics = {
                'total_screws': len(screw_names),
                'successful_screws': len(result.screw_endpoints),
                'base_normal': base_normal,
                'base_center': base_center
            }
            
            # 保存路径可视化（如果需要）
            if input_params.save_paths and result.all_paths:
                try:
                    from utils.path_visualizer import create_path_visualization
                    create_path_visualization(input_params.ct_image, result.all_paths)
                    logging.info("路径可视化已保存")
                except Exception as e:
                    logging.warning(f"路径可视化保存失败: {e}")

            logging.info(f"业务规划完成，耗时: {result.planning_time:.2f}秒")
            return result
            
        except Exception as e:
            result.error_message = f"规划过程中出错: {str(e)}"
            logging.exception("业务规划异常:")
            return result
    
    def _initialize_components(self, input_params: BusinessPlanningInput):
        """初始化组件"""
        self.direction_analyzer = DirectionAnalyzer(
            input_params.ct_image, input_params.mask_image)
        self.bone_density_calculator = BoneDensityCalculator(
            input_params.ct_image, input_params.mask_image)
        self.cone_space_generator = ConeSpaceGenerator(
            input_params.constraint_angle, 50.0, 3.25)  # 默认值，会被覆盖

        # 初始化优化器
        from .optimization import PathOptimizer, ParetoOptimizer
        self.optimizer = PathOptimizer()
        # Pareto优化器：目标角度为0度（与参考方向一致），约束角度用于安全性评分
        self.pareto_optimizer = ParetoOptimizer(target_angle=0.0)
    
    def _calculate_screw_axis(self, screw_name: str, start_point: Point3D, 
                            base_normal: Vector3D, base_center: Point3D) -> Vector3D:
        """计算螺钉的锥轴方向"""
        # 基本方向就是基座法向量
        axis = base_normal
        
        # 为不同螺钉添加轻微偏移以避免干涉
        if screw_name == 'top':
            # 上螺钉稍微向上偏移
            offset_vector = (start_point - base_center).normalize() * 0.1
            axis_array = axis.to_array() + offset_vector.to_array()
            axis = Vector3D.from_array(axis_array).normalize()
        elif screw_name == 'bottom':
            # 下螺钉稍微向下偏移
            offset_vector = (start_point - base_center).normalize() * 0.1
            axis_array = axis.to_array() + offset_vector.to_array()
            axis = Vector3D.from_array(axis_array).normalize()
        
        return axis
    
    def _plan_single_screw(self, start_point: Point3D, axis_direction: Vector3D,
                          screw_spec: ScrewSpec, input_params: BusinessPlanningInput,
                          screw_name: Optional[str] = None) -> PlanningResult:
        """规划单个螺钉"""
        # 更新锥形空间生成器参数
        self.cone_space_generator.screw_length = screw_spec.length
        self.cone_space_generator.screw_radius = screw_spec.radius
        
        # 生成锥形空间
        cone_space = self.cone_space_generator.generate_cone_space(start_point, axis_direction)
        
        # 生成候选路径
        candidate_paths = self.cone_space_generator.generate_candidate_paths(
            cone_space, input_params.radial_resolution, input_params.circumferential_resolution)
        
        # 评估候选路径（使用业务专用的圆环采样评估方法）
        evaluated_paths = self._evaluate_paths_from_base(
            candidate_paths, input_params.integration_resolution, ring_sampling_points=12)
        
        # 筛选有效路径
        valid_paths = [path for path in evaluated_paths if path.is_valid]

        result = PlanningResult()
        if valid_paths:
            if input_params.optimization_method == "pareto":
                # 使用Pareto最优化 - 使用当前螺钉的实际轴向方向
                pareto_solutions = self.pareto_optimizer.optimize_paths_pareto(
                    valid_paths, axis_direction, input_params.max_pareto_solutions)

                if pareto_solutions:
                    # 选择第一个Pareto最优解作为主要解
                    optimal_path = pareto_solutions[0].path
                    result.optimal_paths = [optimal_path]
                    result.success = True
                    # 保存所有Pareto解供后续使用
                    result.pareto_solutions = pareto_solutions
                else:
                    result.error_message = "Pareto优化未找到有效解"
            else:
                # 使用传统加权方法
                optimal_path = max(valid_paths, key=lambda p: p.bone_density_integral)
                result.optimal_paths = [optimal_path]
                result.success = True
        else:
            result.error_message = "没有找到有效的螺钉路径"

        result.all_candidate_paths = candidate_paths
        # 统计终点筛选情况
        endpoint_filtered = len([p for p in evaluated_paths if not p.is_valid and p.bone_density_integral == 0.0])

        result.statistics = {
            'total_candidates': len(candidate_paths),
            'valid_candidates': len(valid_paths),
            'endpoint_filtered': endpoint_filtered  # 因终点超出mask被筛除的数量
        }

        logging.info(f"路径统计 - 总数: {len(candidate_paths)}, 有效: {len(valid_paths)}, 终点超出mask被筛除: {endpoint_filtered}")

        # 收集所有有效路径的起始点和终点（用于可视化）
        result.valid_path_endpoints = [
            (path.start_point, path.end_point) for path in valid_paths
        ]
        
        return result

    def _evaluate_paths_from_base(self, paths: List[ScrewPath],
                                 integration_resolution: int,
                                 ring_sampling_points: int = 12) -> List[ScrewPath]:
        """
        评估从基座表面开始的螺钉路径（圆环采样方法）

        只计算进入肩胛骨掩膜后的路径段，使用圆环采样获得更准确的骨密度评估
        要求终点必须在肩胛骨mask内
        """
        evaluated_paths = []

        for i, path in enumerate(paths):
            if i % 100 == 0:
                logging.info(f"业务路径评估进度: {i}/{len(paths)} (圆环采样方法)")

            # 第一步：检查终点是否在mask内（新增的强制要求）
            if not self.bone_density_calculator.is_point_in_mask(path.end_point):
                path.bone_density_integral = 0.0
                path.is_valid = False
                path.safety_score = 0.0
                logging.debug(f"路径 {i} 终点坐标 ({path.end_point.x:.2f}, {path.end_point.y:.2f}, {path.end_point.z:.2f}) 不在肩胛骨mask内，直接筛除")
                evaluated_paths.append(path)
                continue

            # 获取路径上的中心线采样点
            center_points = path.get_points_along_path(integration_resolution)
            direction_vector = path.get_direction_vector()

            # 找到第一个进入掩膜的点
            mask_entry_index = None
            for j, point in enumerate(center_points):
                if self.bone_density_calculator.is_point_in_mask(point):
                    mask_entry_index = j
                    break

            if mask_entry_index is None:
                # 整条路径都不进入掩膜，标记为无效
                path.bone_density_integral = 0.0
                path.is_valid = False
                path.safety_score = 0.0
                logging.debug(f"路径 {i} 完全不进入肩胛骨掩膜")
                evaluated_paths.append(path)
                continue

            # 只计算进入掩膜后的部分（圆环采样）
            mask_center_points = center_points[mask_entry_index:]

            total_integral = 0.0
            valid_rings = 0
            path_is_valid = True

            for center_point in mask_center_points:
                # 生成当前位置的圆环采样点
                ring_points = self.bone_density_calculator.generate_ring_sampling_points(
                    center_point, direction_vector, path.radius, ring_sampling_points)

                # 计算圆环上各点的骨密度
                ring_bone_densities = []
                valid_ring_points = 0

                for ring_point in ring_points:
                    # 检查点是否在肩胛骨掩膜内
                    if not self.bone_density_calculator.is_point_in_mask(ring_point):
                        continue

                    bone_density = self.bone_density_calculator.get_bone_density_at_point(ring_point)
                    if bone_density is not None:
                        ring_bone_densities.append(bone_density)
                        valid_ring_points += 1

                # 如果圆环上有效点太少，认为这个圆环无效
                if valid_ring_points < ring_sampling_points * 0.3:  # 至少30%的点有效
                    path_is_valid = False
                    continue

                # 计算圆环的平均骨密度
                if ring_bone_densities:
                    ring_average_density = sum(ring_bone_densities) / len(ring_bone_densities)
                    total_integral += ring_average_density
                    valid_rings += 1

            # 计算有效路径段的长度比例
            mask_length_ratio = len(mask_center_points) / len(center_points)

            # 如果有效圆环数太少，认为路径无效
            min_valid_rings = max(1, len(mask_center_points) * 0.5)
            if valid_rings < min_valid_rings:
                logging.debug(f"路径 {i} 在掩膜内有效圆环过少: {valid_rings}/{len(mask_center_points)}")
                path.bone_density_integral = 0.0
                path.is_valid = False
            else:
                # 计算平均骨密度并乘以有效路径长度
                if valid_rings > 0:
                    average_density = total_integral / valid_rings
                    effective_length = path.get_path_length() * mask_length_ratio
                    path.bone_density_integral = average_density * effective_length
                    path.is_valid = path_is_valid
                else:
                    path.bone_density_integral = 0.0
                    path.is_valid = False

            # 计算安全评分
            if path.is_valid and path.bone_density_integral > 0:
                path.safety_score = path.bone_density_integral
            else:
                path.safety_score = 0.0

            # 计算覆盖率（只计算mask内部分的覆盖率）
            if path.is_valid:
                try:
                    # 使用新的方法：只计算进入mask后的圆柱体覆盖率
                    coverage_ratio = self.bone_density_calculator.calculate_cylinder_coverage_mask_only(path)
                    path.coverage_ratio = coverage_ratio

                    # 应用95%覆盖率安全阈值
                    coverage_threshold = 0.95
                    if coverage_ratio < coverage_threshold:
                        path.is_valid = False
                        path.safety_score = 0.0
                        logging.debug(f"路径 {i} 覆盖率 {coverage_ratio:.3f} < {coverage_threshold:.3f}，标记为不安全")
                    else:
                        logging.debug(f"路径 {i} 覆盖率: {coverage_ratio:.3f} ≥ {coverage_threshold:.3f}，安全")

                except Exception as e:
                    logging.warning(f"路径 {i} 覆盖率计算失败: {e}")
                    path.coverage_ratio = 0.0
                    path.is_valid = False
                    path.safety_score = 0.0
            else:
                path.coverage_ratio = 0.0

            # 记录掩膜进入比例
            if hasattr(path, 'mask_entry_ratio'):
                path.mask_entry_ratio = mask_length_ratio

            evaluated_paths.append(path)

        # 统计结果
        valid_paths = [p for p in evaluated_paths if p.is_valid]
        logging.info(f"业务路径评估完成:")
        logging.info(f"  总路径数: {len(evaluated_paths)}")
        logging.info(f"  有效路径数: {len(valid_paths)}")
        logging.info(f"  通过率: {len(valid_paths)/len(evaluated_paths)*100:.1f}%")

        return evaluated_paths
