"""
图像处理工具模块

提供CT图像处理和3D重建功能
"""

import numpy as np
import SimpleITK as sitk
from skimage import measure
import vtk
from vtk.util import numpy_support
from typing import Tuple, Optional, List
import logging

try:
    from .geometry import Point3D
except ImportError:
    from utils.geometry import Point3D


class CTImageProcessor:
    """CT图像处理器"""
    
    def __init__(self):
        """初始化CT图像处理器"""
        logging.info("CT图像处理器初始化完成")
    
    def load_ct_image(self, file_path: str) -> sitk.Image:
        """
        加载CT图像
        
        Args:
            file_path: 图像文件路径
            
        Returns:
            SimpleITK图像对象
        """
        try:
            image = sitk.ReadImage(file_path)
            logging.info(f"成功加载CT图像: {file_path}")
            logging.info(f"图像尺寸: {image.GetSize()}")
            logging.info(f"体素间距: {image.GetSpacing()}")
            logging.info(f"图像原点: {image.GetOrigin()}")
            return image
        except Exception as e:
            logging.error(f"加载CT图像失败: {e}")
            raise
    
    def load_mask_image(self, file_path: str) -> sitk.Image:
        """
        加载掩膜图像
        
        Args:
            file_path: 掩膜文件路径
            
        Returns:
            SimpleITK掩膜图像对象
        """
        try:
            mask = sitk.ReadImage(file_path)
            logging.info(f"成功加载掩膜图像: {file_path}")
            return mask
        except Exception as e:
            logging.error(f"加载掩膜图像失败: {e}")
            raise
    
    def resample_image(self, image: sitk.Image, new_spacing: Tuple[float, float, float],
                      interpolator=sitk.sitkLinear) -> sitk.Image:
        """
        重采样图像
        
        Args:
            image: 输入图像
            new_spacing: 新的体素间距
            interpolator: 插值方法
            
        Returns:
            重采样后的图像
        """
        original_spacing = image.GetSpacing()
        original_size = image.GetSize()
        
        # 计算新的图像尺寸
        new_size = [
            int(round(original_size[0] * original_spacing[0] / new_spacing[0])),
            int(round(original_size[1] * original_spacing[1] / new_spacing[1])),
            int(round(original_size[2] * original_spacing[2] / new_spacing[2]))
        ]
        
        # 设置重采样参数
        resampler = sitk.ResampleImageFilter()
        resampler.SetOutputSpacing(new_spacing)
        resampler.SetSize(new_size)
        resampler.SetOutputDirection(image.GetDirection())
        resampler.SetOutputOrigin(image.GetOrigin())
        resampler.SetTransform(sitk.Transform())
        resampler.SetDefaultPixelValue(image.GetPixelIDValue())
        resampler.SetInterpolator(interpolator)
        
        resampled_image = resampler.Execute(image)
        logging.info(f"图像重采样完成: {original_size} -> {new_size}")
        
        return resampled_image
    
    # 注意：分割功能已移除，因为用户将直接提供分割好的掩膜
    # 如果需要阈值分割功能，请使用外部工具（如3D Slicer、ITK-SNAP等）

    def validate_mask_image(self, mask_image: sitk.Image) -> bool:
        """
        验证掩膜图像的有效性

        Args:
            mask_image: 掩膜图像

        Returns:
            是否为有效掩膜
        """
        if mask_image is None:
            logging.error("掩膜图像为空")
            return False

        # 检查掩膜是否包含非零值
        stats = sitk.StatisticsImageFilter()
        stats.Execute(mask_image)

        if stats.GetMaximum() == 0:
            logging.error("掩膜图像不包含任何前景区域")
            return False

        logging.info(f"掩膜验证通过，前景像素数量: {stats.GetSum()}")
        return True

    def clean_mask_image(self, mask_image: sitk.Image) -> sitk.Image:
        """
        清理掩膜图像（可选的后处理）

        Args:
            mask_image: 输入掩膜图像

        Returns:
            清理后的掩膜图像
        """
        # 获取数据范围
        stats = sitk.StatisticsImageFilter()
        stats.Execute(mask_image)
        min_val = stats.GetMinimum()
        max_val = stats.GetMaximum()

        # 根据数据范围设置阈值
        if max_val <= 1.0:
            # 已经是0-1的二值掩膜，直接返回
            logging.info("掩膜已经是二值格式，无需清理")
            return mask_image
        elif max_val <= 255:
            # 8位掩膜
            threshold = 127.5
            upper_threshold = 255.0
        else:
            # 其他范围
            threshold = (min_val + max_val) / 2
            upper_threshold = max_val

        # 确保掩膜为二值图像
        binary_mask = sitk.BinaryThreshold(mask_image, threshold, upper_threshold, 1, 0)

        # 可选：形态学操作去除小的噪声
        # 注释掉以保持原始掩膜不变
        # binary_mask = sitk.BinaryMorphologicalClosing(binary_mask, [2, 2, 2])
        # binary_mask = sitk.BinaryMorphologicalOpening(binary_mask, [1, 1, 1])

        logging.info("掩膜图像清理完成")
        return binary_mask
    
    def create_3d_model_marching_cubes(self, image: sitk.Image,
                                     threshold: float = 200.0) -> vtk.vtkPolyData:
        """
        使用Marching Cubes算法创建3D模型

        Args:
            image: 输入图像
            threshold: 等值面阈值

        Returns:
            VTK多边形数据
        """
        # 转换为numpy数组
        image_array = sitk.GetArrayFromImage(image)
        spacing = image.GetSpacing()

        # 检查数据范围并调整阈值
        min_val = float(np.min(image_array))
        max_val = float(np.max(image_array))
        logging.info(f"图像数据范围: [{min_val}, {max_val}]")

        # 对于二值掩膜，使用中间值作为阈值
        if max_val <= 1.0:
            # 二值掩膜 (0-1)
            threshold = 0.5
        elif max_val <= 255:
            # 可能是8位掩膜 (0-255)
            threshold = 127.5
        else:
            # 使用原始阈值或调整到数据范围内
            threshold = min(threshold, (min_val + max_val) / 2)

        logging.info(f"使用阈值: {threshold}")

        # 使用scikit-image的marching cubes
        try:
            vertices, faces, _, _ = measure.marching_cubes(
                image_array, level=threshold, spacing=spacing)
            
            # 创建VTK点
            points = vtk.vtkPoints()
            for vertex in vertices:
                points.InsertNextPoint(vertex[2], vertex[1], vertex[0])  # 注意坐标顺序
            
            # 创建VTK单元
            cells = vtk.vtkCellArray()
            for face in faces:
                triangle = vtk.vtkTriangle()
                triangle.GetPointIds().SetId(0, face[0])
                triangle.GetPointIds().SetId(1, face[1])
                triangle.GetPointIds().SetId(2, face[2])
                cells.InsertNextCell(triangle)
            
            # 创建VTK多边形数据
            polydata = vtk.vtkPolyData()
            polydata.SetPoints(points)
            polydata.SetPolys(cells)
            
            # 计算法向量
            normals_filter = vtk.vtkPolyDataNormals()
            normals_filter.SetInputData(polydata)
            normals_filter.ComputePointNormalsOn()
            normals_filter.ComputeCellNormalsOn()
            normals_filter.Update()
            
            result = normals_filter.GetOutput()
            logging.info(f"3D模型创建完成，顶点数: {result.GetNumberOfPoints()}, "
                        f"面数: {result.GetNumberOfCells()}")
            
            return result
            
        except Exception as e:
            logging.error(f"Marching Cubes算法执行失败: {e}")
            raise
    
    def smooth_surface(self, polydata: vtk.vtkPolyData, 
                      iterations: int = 50, relaxation_factor: float = 0.1) -> vtk.vtkPolyData:
        """
        平滑表面
        
        Args:
            polydata: 输入多边形数据
            iterations: 迭代次数
            relaxation_factor: 松弛因子
            
        Returns:
            平滑后的多边形数据
        """
        smoother = vtk.vtkSmoothPolyDataFilter()
        smoother.SetInputData(polydata)
        smoother.SetNumberOfIterations(iterations)
        smoother.SetRelaxationFactor(relaxation_factor)
        smoother.FeatureEdgeSmoothingOff()
        smoother.BoundarySmoothingOn()
        smoother.Update()
        
        logging.info(f"表面平滑完成，迭代次数: {iterations}")
        return smoother.GetOutput()
    
    def decimate_surface(self, polydata: vtk.vtkPolyData, 
                        target_reduction: float = 0.5) -> vtk.vtkPolyData:
        """
        简化表面（减少面数）
        
        Args:
            polydata: 输入多边形数据
            target_reduction: 目标简化比例（0-1）
            
        Returns:
            简化后的多边形数据
        """
        decimator = vtk.vtkDecimatePro()
        decimator.SetInputData(polydata)
        decimator.SetTargetReduction(target_reduction)
        decimator.PreserveTopologyOn()
        decimator.Update()
        
        result = decimator.GetOutput()
        original_faces = polydata.GetNumberOfCells()
        reduced_faces = result.GetNumberOfCells()
        
        logging.info(f"表面简化完成: {original_faces} -> {reduced_faces} 面 "
                    f"(简化率: {(1 - reduced_faces/original_faces)*100:.1f}%)")
        
        return result
    
    def create_bone_surface_model_from_mask(self, mask_image: sitk.Image,
                                          smooth_iterations: int = 50,
                                          decimate_ratio: float = 0.7) -> vtk.vtkPolyData:
        """
        从掩膜图像创建骨骼表面模型

        Args:
            mask_image: 骨骼掩膜图像（已分割好的）
            smooth_iterations: 平滑迭代次数
            decimate_ratio: 简化比例

        Returns:
            骨骼表面模型
        """
        logging.info("开始从掩膜创建骨骼表面模型...")

        # 验证掩膜
        if not self.validate_mask_image(mask_image):
            raise ValueError("无效的掩膜图像")

        # 清理掩膜（可选）
        clean_mask = self.clean_mask_image(mask_image)

        # 获取清理后掩膜的数据范围，设置合适的阈值
        stats = sitk.StatisticsImageFilter()
        stats.Execute(clean_mask)
        min_val = stats.GetMinimum()
        max_val = stats.GetMaximum()

        # 根据数据范围设置阈值
        if min_val == max_val:
            # 如果只有一个值，说明掩膜有问题，使用稍小的阈值
            threshold = min_val - 0.1
        elif max_val <= 1.0:
            threshold = (min_val + max_val) / 2
        elif max_val <= 255:
            threshold = (min_val + max_val) / 2
        else:
            threshold = (min_val + max_val) / 2

        # Marching Cubes重建
        surface = self.create_3d_model_marching_cubes(clean_mask, threshold)

        # 表面平滑
        if smooth_iterations > 0:
            surface = self.smooth_surface(surface, smooth_iterations)

        # 表面简化
        if decimate_ratio > 0:
            surface = self.decimate_surface(surface, decimate_ratio)

        logging.info("骨骼表面模型创建完成")
        return surface

    def create_bone_surface_model(self, ct_image=None,
                                bone_threshold: float = 200.0,
                                smooth_iterations: int = 50,
                                decimate_ratio: float = 0.7) -> vtk.vtkPolyData:
        """
        创建骨骼表面模型（保留兼容性，但建议使用 create_bone_surface_model_from_mask）

        注意：此方法已弃用，请直接提供分割好的掩膜并使用 create_bone_surface_model_from_mask

        Args:
            ct_image: CT图像（已弃用）
            bone_threshold: 骨骼阈值（已弃用）
            smooth_iterations: 平滑迭代次数
            decimate_ratio: 简化比例

        Returns:
            骨骼表面模型
        """
        logging.warning("create_bone_surface_model 方法已弃用，请使用 create_bone_surface_model_from_mask")
        raise NotImplementedError("请直接提供分割好的掩膜并使用 create_bone_surface_model_from_mask 方法")
    
    def get_image_bounds(self, image: sitk.Image) -> Tuple[Tuple[float, float], 
                                                          Tuple[float, float], 
                                                          Tuple[float, float]]:
        """
        获取图像的物理边界
        
        Args:
            image: 输入图像
            
        Returns:
            ((x_min, x_max), (y_min, y_max), (z_min, z_max))
        """
        size = image.GetSize()
        spacing = image.GetSpacing()
        origin = image.GetOrigin()
        
        x_max = origin[0] + (size[0] - 1) * spacing[0]
        y_max = origin[1] + (size[1] - 1) * spacing[1]
        z_max = origin[2] + (size[2] - 1) * spacing[2]
        
        return ((origin[0], x_max), (origin[1], y_max), (origin[2], z_max))
    
    def is_point_in_image_bounds(self, point: Point3D, image: sitk.Image) -> bool:
        """
        检查点是否在图像边界内
        
        Args:
            point: 3D点
            image: 图像
            
        Returns:
            True如果点在边界内，False否则
        """
        bounds = self.get_image_bounds(image)
        
        return (bounds[0][0] <= point.x <= bounds[0][1] and
                bounds[1][0] <= point.y <= bounds[1][1] and
                bounds[2][0] <= point.z <= bounds[2][1])
