"""
路径规划主算法模块

实现基于锥形空间和骨密度评估的自动螺钉路径规划
"""

import time
import math
import logging
from typing import List, Tuple, Optional
import SimpleITK as sitk

try:
    from ..utils.geometry import Point3D, Vector3D, ScrewPath, PlanningResult
    from .bone_density import BoneDensityCalculator
    from .cone_space import ConeSpaceGenerator
except ImportError:
    from utils.geometry import Point3D, Vector3D, ScrewPath, PlanningResult
    from core.bone_density import BoneDensityCalculator
    from core.cone_space import ConeSpaceGenerator


class PathPlanner:
    """路径规划器"""
    
    def __init__(self, ct_image: sitk.Image,
                 mask_image: Optional[sitk.Image] = None,
                 constraint_angle: float = 45.0,
                 screw_length: float = 38.97,
                 screw_radius: float = 1.65,
                 radial_resolution: int = 30,
                 circumferential_resolution: int = 150,
                 integration_resolution: int = 30):
        """
        初始化路径规划器

        Args:
            ct_image: CT图像
            mask_image: 肩胛骨掩膜图像（可选）
            constraint_angle: 约束角度（度）
            screw_length: 螺钉长度 (mm)
            screw_radius: 螺钉半径 (mm)
            radial_resolution: 径向分辨率
            circumferential_resolution: 周向分辨率
            integration_resolution: 积分分辨率
        """
        self.ct_image = ct_image
        self.mask_image = mask_image
        self.constraint_angle = constraint_angle
        self.screw_length = screw_length
        self.screw_radius = screw_radius
        self.radial_resolution = radial_resolution
        self.circumferential_resolution = circumferential_resolution
        self.integration_resolution = integration_resolution

        # 初始化子模块
        self.bone_density_calculator = BoneDensityCalculator(ct_image, mask_image)
        self.cone_space_generator = ConeSpaceGenerator(
            constraint_angle, screw_length, screw_radius)

        logging.info("路径规划器初始化完成")
        if mask_image is not None:
            logging.info("已加载肩胛骨掩膜，螺钉路径将限制在肩胛骨范围内")
        logging.info(f"规划参数 - 约束角度: {constraint_angle}°, "
                    f"径向分辨率: {radial_resolution}, "
                    f"周向分辨率: {circumferential_resolution}")
    
    def validate_reference_points(self, reference_points: List[Point3D]) -> bool:
        """
        验证参考点的有效性
        
        Args:
            reference_points: 参考点列表
            
        Returns:
            True如果参考点有效，False否则
        """
        if len(reference_points) < 4:
            logging.error("参考点数量不足，至少需要4个点")
            return False
        
        # 检查点是否共线
        if len(reference_points) >= 3:
            p1, p2, p3 = reference_points[:3]
            v1 = p2 - p1
            v2 = p3 - p1
            cross_product = v1.cross(v2)
            if cross_product.magnitude() < 1e-6:
                logging.error("前三个参考点共线，无法确定平面")
                return False
        
        logging.info("参考点验证通过")
        return True
    
    def plan_screw_paths(self, reference_points: List[Point3D]) -> PlanningResult:
        """
        执行螺钉路径规划

        Args:
            reference_points: 参考点列表 [p1, p2, p3, p4]

        Returns:
            规划结果
        """
        start_time = time.time()
        result = PlanningResult()
        
        try:
            # 验证参考点
            if not self.validate_reference_points(reference_points):
                result.error_message = "参考点验证失败"
                return result
            
            logging.info("开始螺钉路径规划...")
            
            # 计算假体参数
            p1, epsilon, m = self.cone_space_generator.calculate_prosthesis_parameters(
                reference_points)
            
            logging.info(f"假体基座中心: {p1}")
            logging.info(f"法向量: {epsilon}")
            logging.info(f"方向向量: {m}")
            
            # 生成候选路径
            paths1, paths2 = self.cone_space_generator.generate_paths_for_screw_pair(
                p1, reference_points[3], epsilon, m,
                radial_resolution=self.radial_resolution,
                circumferential_resolution=self.circumferential_resolution
            )
            
            # 角度约束过滤
            filtered_paths1, filtered_paths2 = self.cone_space_generator.filter_paths_by_angle_constraint(
                paths1, paths2, self.constraint_angle)
            
            # 合并所有候选路径
            all_candidate_paths = filtered_paths1 + filtered_paths2
            result.all_candidate_paths = all_candidate_paths
            result.statistics['total_candidates'] = len(all_candidate_paths)
            
            logging.info(f"开始评估 {len(all_candidate_paths)} 条候选路径...")
            
            # 评估所有候选路径（使用圆环采样方法）
            evaluated_paths = self.bone_density_calculator.batch_evaluate_paths(
                all_candidate_paths, self.integration_resolution, ring_sampling_points=12)
            
            # 筛选有效路径
            valid_paths = [path for path in evaluated_paths if path.is_valid]
            result.statistics['valid_candidates'] = len(valid_paths)
            
            if not valid_paths:
                result.error_message = "没有找到有效的螺钉路径"
                logging.warning("没有找到有效的螺钉路径")
                return result
            
            # 分别为两个螺钉选择最优路径
            valid_paths1 = [path for path in evaluated_paths 
                          if path in filtered_paths1 and path.is_valid]
            valid_paths2 = [path for path in evaluated_paths 
                          if path in filtered_paths2 and path.is_valid]
            
            if valid_paths1:
                best_path1 = max(valid_paths1, key=lambda p: p.bone_density_integral)
                result.add_optimal_path(best_path1)
                logging.info(f"螺钉1最优路径骨密度积分: {best_path1.bone_density_integral:.2f}")
            
            if valid_paths2:
                best_path2 = max(valid_paths2, key=lambda p: p.bone_density_integral)
                result.add_optimal_path(best_path2)
                logging.info(f"螺钉2最优路径骨密度积分: {best_path2.bone_density_integral:.2f}")
            
            # 计算统计信息
            if valid_paths:
                bone_densities = [path.bone_density_integral for path in valid_paths]
                result.statistics['average_bone_density'] = sum(bone_densities) / len(bone_densities)
                result.statistics['max_bone_density'] = max(bone_densities)
                result.statistics['min_bone_density'] = min(bone_densities)
            
            result.success = len(result.optimal_paths) > 0
            
        except Exception as e:
            logging.error(f"路径规划过程中出错: {e}")
            result.error_message = str(e)
            result.success = False
        
        finally:
            result.planning_time = time.time() - start_time
            logging.info(f"路径规划完成，耗时: {result.planning_time:.2f}秒")
        
        return result
    
    def plan_single_screw_path(self, start_point: Point3D,
                             axis_direction: Vector3D) -> PlanningResult:
        """
        规划单个螺钉路径

        Args:
            start_point: 螺钉起始点
            axis_direction: 锥轴方向

        Returns:
            规划结果
        """
        start_time = time.time()
        result = PlanningResult()
        
        try:
            logging.info("开始单螺钉路径规划...")
            
            # 生成锥形空间
            cone_space = self.cone_space_generator.generate_cone_space(
                start_point, axis_direction)
            
            # 生成候选路径
            candidate_paths = self.cone_space_generator.generate_candidate_paths(
                cone_space, self.radial_resolution, self.circumferential_resolution)
            
            result.all_candidate_paths = candidate_paths
            result.statistics['total_candidates'] = len(candidate_paths)
            
            # 评估候选路径（使用圆环采样方法）
            evaluated_paths = self.bone_density_calculator.batch_evaluate_paths(
                candidate_paths, self.integration_resolution, ring_sampling_points=12)
            
            # 筛选有效路径
            valid_paths = [path for path in evaluated_paths if path.is_valid]
            result.statistics['valid_candidates'] = len(valid_paths)
            
            if not valid_paths:
                result.error_message = "没有找到有效的螺钉路径"
                return result
            
            # 选择最优路径
            best_path = max(valid_paths, key=lambda p: p.bone_density_integral)
            result.add_optimal_path(best_path)
            
            # 计算统计信息
            bone_densities = [path.bone_density_integral for path in valid_paths]
            result.statistics['average_bone_density'] = sum(bone_densities) / len(bone_densities)
            result.statistics['max_bone_density'] = max(bone_densities)
            result.statistics['min_bone_density'] = min(bone_densities)
            
            result.success = True
            
        except Exception as e:
            logging.error(f"单螺钉路径规划过程中出错: {e}")
            result.error_message = str(e)
            result.success = False
        
        finally:
            result.planning_time = time.time() - start_time
            logging.info(f"单螺钉路径规划完成，耗时: {result.planning_time:.2f}秒")
        
        return result
    
    def update_parameters(self, **kwargs):
        """
        更新规划参数
        
        Args:
            **kwargs: 参数字典
        """
        if 'constraint_angle' in kwargs:
            self.constraint_angle = kwargs['constraint_angle']
            self.cone_space_generator.constraint_angle = math.radians(kwargs['constraint_angle'])
        
        if 'radial_resolution' in kwargs:
            self.radial_resolution = kwargs['radial_resolution']
        
        if 'circumferential_resolution' in kwargs:
            self.circumferential_resolution = kwargs['circumferential_resolution']
        
        if 'integration_resolution' in kwargs:
            self.integration_resolution = kwargs['integration_resolution']
        
        logging.info("规划参数已更新")
    
    def get_planning_statistics(self, result: PlanningResult) -> dict:
        """
        获取规划统计信息
        
        Args:
            result: 规划结果
            
        Returns:
            统计信息字典
        """
        stats = {
            'success': result.success,
            'planning_time': result.planning_time,
            'total_candidates': result.statistics['total_candidates'],
            'valid_candidates': result.statistics['valid_candidates'],
            'optimal_paths_count': len(result.optimal_paths),
            'success_rate': result.statistics['valid_candidates'] / max(1, result.statistics['total_candidates'])
        }
        
        if result.optimal_paths:
            best_path = result.get_best_path()
            if best_path:
                stats['best_bone_density_integral'] = best_path.bone_density_integral
                stats['best_path_length'] = best_path.get_path_length()
        
        return stats
