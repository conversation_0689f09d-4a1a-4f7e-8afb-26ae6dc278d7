# 用户手册

## 系统简介

肩盂假体基座螺钉植入路径规划系统是一个基于骨密度评估和锥形空间路径积分的自动手术规划工具。该系统能够帮助医生为反向肩关节置换术制定最优的螺钉植入路径。

## 系统要求

### 硬件要求
- **处理器**: Intel i5 或 AMD 同等级别以上
- **内存**: 8GB RAM 以上（推荐16GB）
- **显卡**: 支持OpenGL 3.0以上的独立显卡
- **存储**: 至少2GB可用磁盘空间

### 软件要求
- **操作系统**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **Python**: 3.8 或更高版本
- **依赖库**: 详见 requirements.txt

## 安装指南

### 1. 环境准备
```bash
# 克隆项目
git clone <repository_url>
cd bone-mineral-density-calculation/ScrewPlanning

# 安装依赖
pip install -r ../requirements.txt
```

### 2. 启动系统
```bash
# 启动螺钉规划系统
python src/main.py
```

## 使用流程

### 第一步：数据准备

#### 1.1 CT图像要求
- **格式**: NIfTI (.nii 或 .nii.gz)
- **分辨率**: 建议体素间距 ≤ 1mm
- **范围**: 包含完整的肩胛骨区域
- **对比度**: 骨骼与软组织对比度良好

#### 1.2 掩膜图像（可选）
- **格式**: NIfTI (.nii 或 .nii.gz)
- **内容**: 肩胛骨分割掩膜
- **标注**: 二值图像，骨骼区域为1，背景为0

### 第二步：加载数据

1. **启动系统**后，点击菜单栏"文件" → "加载CT图像"
2. 选择CT图像文件（.nii.gz格式）
3. （可选）点击"文件" → "加载骨骼掩膜"选择掩膜文件
4. 系统将自动显示3D骨骼模型

### 第三步：参考点选择

#### 3.1 选择顺序
按照以下顺序依次选择4个参考点：

1. **P1 - 假体基座中心点**
   - 位置：肩盂关节面的中心
   - 作用：确定假体基座的位置

2. **P2 - 平面参考点1**
   - 位置：肩盂关节面边缘
   - 作用：与P1、P3共同确定假体平面

3. **P3 - 平面参考点2**
   - 位置：肩盂关节面另一边缘
   - 作用：与P1、P2共同确定假体平面

4. **P4 - 方向参考点**
   - 位置：确定螺钉方向的参考点
   - 作用：确定两个螺钉的相对位置

#### 3.2 选择操作
1. 在"参考点选择"标签页中点击"添加点"按钮
2. 在3D视图中点击相应的解剖位置
3. 系统会自动添加彩色标记点
4. 重复操作直到选择完4个点

#### 3.3 注意事项
- 确保点选择准确，这直接影响规划结果
- 可以点击"清除所有"重新选择
- 选择的点会在3D视图中显示为不同颜色的球体

### 第四步：参数设置

在"规划参数"标签页中调整以下参数：

#### 4.1 基本参数
- **约束角度**: 螺钉的最大偏离角度（默认45°）
- **螺钉长度**: 螺钉的长度（默认38.97mm）
- **螺钉半径**: 螺钉的半径（默认1.65mm）

#### 4.2 高级参数
- **径向分辨率**: 锥形空间径向采样密度（默认30）
- **周向分辨率**: 锥形空间周向采样密度（默认150）
- **积分分辨率**: 路径积分采样密度（默认30）
- **骨骼阈值**: 骨骼识别的HU值阈值（默认200 HU）

### 第五步：执行规划

1. 确认已选择4个参考点
2. 检查参数设置
3. 点击"开始规划"按钮
4. 等待规划完成（通常需要几秒到几分钟）

### 第六步：结果分析

#### 6.1 结果概览
在"规划结果"标签页查看：
- **规划状态**: 成功/失败
- **规划耗时**: 算法执行时间
- **最优路径数**: 找到的最优螺钉路径数量

#### 6.2 详细结果
查看每条最优路径的详细信息：
- **起始点坐标**: 螺钉植入起点
- **终止点坐标**: 螺钉植入终点
- **路径长度**: 螺钉路径的实际长度
- **骨密度积分**: 路径的骨密度积分值
- **安全评分**: 路径的综合安全评分

#### 6.3 3D可视化
在3D视图中查看：
- **螺钉路径**: 以彩色管道显示
- **参考点**: 彩色球体标记
- **骨骼模型**: 半透明骨骼表面

### 第七步：结果导出

1. 点击菜单栏"文件" → "导出规划结果"
2. 选择导出目录
3. 系统将生成以下文件：
   - **规划结果JSON文件**: 包含完整的规划数据
   - **规划报告Markdown文件**: 可读性强的结果报告
   - **路径数据CSV文件**: 便于进一步分析的表格数据

## 操作技巧

### 3D视图操作
- **旋转**: 左键拖拽
- **缩放**: 鼠标滚轮
- **平移**: 中键拖拽或Shift+左键拖拽
- **重置视图**: 菜单栏"视图" → "重置视图"

### 参数调优建议
- **高骨密度患者**: 可适当降低骨骼阈值
- **骨质疏松患者**: 可适当提高骨骼阈值
- **精确规划**: 增加分辨率参数值
- **快速规划**: 降低分辨率参数值

## 常见问题

### Q1: 系统启动失败
**A**: 检查Python环境和依赖库是否正确安装，确保VTK库版本兼容。

### Q2: CT图像无法加载
**A**: 确认文件格式为NIfTI (.nii或.nii.gz)，检查文件是否损坏。

### Q3: 3D模型显示异常
**A**: 检查显卡驱动是否支持OpenGL，尝试调整骨骼阈值参数。

### Q4: 规划结果不理想
**A**: 检查参考点选择是否准确，尝试调整约束角度和阈值参数。

### Q5: 规划时间过长
**A**: 降低分辨率参数值，或检查CT图像尺寸是否过大。

## 技术支持

如遇到技术问题，请提供以下信息：
- 系统版本和操作系统信息
- 错误信息截图
- 使用的CT图像参数
- 详细的操作步骤

## 免责声明

本系统仅供研究和教学使用，不能替代医生的专业判断。在实际临床应用中，请务必结合医生的专业经验和其他诊断信息做出最终决策。
