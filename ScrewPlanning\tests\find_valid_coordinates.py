#!/usr/bin/env python3
"""
寻找有效的螺钉坐标

在肩胛骨掩膜内找到合适的螺钉植入坐标
"""

import sys
import os
import logging
import numpy as np

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def find_valid_coordinates():
    """寻找有效的螺钉坐标"""
    try:
        # 导入必要的模块
        from core.bone_density import BoneDensityCalculator
        from utils.geometry import Point3D
        from utils.io_utils import DataLoader
        import SimpleITK as sitk
        
        print("=" * 60)
        print("寻找有效的螺钉坐标")
        print("=" * 60)
        
        # 加载数据
        data_dir = os.path.join(current_dir, 'examples', 'PlanData')
        ct_file = os.path.join(data_dir, 'CT.nii.gz')
        mask_file = os.path.join(data_dir, 'scapula_mask.nii.gz')
        
        loader = DataLoader()
        ct_image = loader.load_ct_image(ct_file)
        mask_image = loader.load_mask_image(mask_file)
        
        # 创建骨密度计算器
        calculator = BoneDensityCalculator(ct_image, mask_image)
        
        # 获取掩膜数组
        mask_array = calculator.mask_array
        
        # 找到掩膜中的有效区域
        valid_indices = np.where(mask_array > 0)
        
        if len(valid_indices[0]) == 0:
            print("❌ 掩膜中没有有效区域")
            return False
        
        print(f"✅ 找到 {len(valid_indices[0])} 个有效体素")
        
        # 计算有效区域的边界
        min_i, max_i = valid_indices[0].min(), valid_indices[0].max()
        min_j, max_j = valid_indices[1].min(), valid_indices[1].max()
        min_k, max_k = valid_indices[2].min(), valid_indices[2].max()
        
        print(f"📊 有效区域边界（图像坐标）:")
        print(f"  i: {min_i} - {max_i}")
        print(f"  j: {min_j} - {max_j}")
        print(f"  k: {min_k} - {max_k}")
        
        # 选择几个代表性的点
        center_i = (min_i + max_i) // 2
        center_j = (min_j + max_j) // 2
        center_k = (min_k + max_k) // 2
        
        # 确保选择的点在掩膜内
        candidate_points = []
        
        # 中心点
        if mask_array[center_i, center_j, center_k] > 0:
            candidate_points.append(('center_candidate', center_i, center_j, center_k))
        
        # 上方点
        top_i = min_i + (max_i - min_i) // 4
        if mask_array[top_i, center_j, center_k] > 0:
            candidate_points.append(('top_candidate', top_i, center_j, center_k))
        
        # 下方点
        bottom_i = max_i - (max_i - min_i) // 4
        if mask_array[bottom_i, center_j, center_k] > 0:
            candidate_points.append(('bottom_candidate', bottom_i, center_j, center_k))
        
        # 如果没有找到合适的点，随机选择一些
        if len(candidate_points) < 3:
            print("⚠️  使用随机采样寻找有效点...")
            # 随机选择一些有效点
            random_indices = np.random.choice(len(valid_indices[0]), min(10, len(valid_indices[0])), replace=False)
            for idx in random_indices:
                i, j, k = valid_indices[0][idx], valid_indices[1][idx], valid_indices[2][idx]
                candidate_points.append((f'random_{len(candidate_points)}', i, j, k))
                if len(candidate_points) >= 3:
                    break
        
        print(f"\n📍 候选坐标点:")
        print("-" * 60)
        
        valid_coords = {}
        for name, i, j, k in candidate_points[:3]:  # 只取前3个
            # 转换为物理坐标
            # 注意：SimpleITK使用(x,y,z)顺序，numpy使用(z,y,x)顺序
            physical_point = ct_image.TransformIndexToPhysicalPoint([int(k), int(j), int(i)])
            coord = Point3D(physical_point[0], physical_point[1], physical_point[2])
            
            # 验证坐标
            in_mask = calculator.is_point_in_mask(coord)
            hu_value = calculator.get_hu_value_at_point(coord)
            bone_density = calculator.get_bone_density_at_point(coord)
            
            print(f"\n{name}:")
            print(f"  图像坐标: ({i}, {j}, {k})")
            print(f"  物理坐标: ({coord.x:.3f}, {coord.y:.3f}, {coord.z:.3f})")
            print(f"  在掩膜内: {'✅' if in_mask else '❌'}")
            print(f"  HU值: {hu_value:.1f}" if hu_value is not None else "  HU值: 无法获取")
            print(f"  骨密度: {bone_density:.1f} mg/cc" if bone_density is not None else "  骨密度: 无法获取")
            
            if in_mask:
                valid_coords[name] = coord
        
        # 生成新的坐标文件
        if len(valid_coords) >= 3:
            coord_names = list(valid_coords.keys())
            new_coords = {
                'center': valid_coords[coord_names[0]],
                'top': valid_coords[coord_names[1]],
                'bottom': valid_coords[coord_names[2]]
            }
            
            # 保存到JSON文件
            import json
            output_file = os.path.join(data_dir, 'valid_screw_coords.json')
            output_data = {}
            for name, coord in new_coords.items():
                output_data[name] = {
                    'x': coord.x,
                    'y': coord.y,
                    'z': coord.z
                }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 有效坐标已保存到: {output_file}")
            print("\n🎯 建议的螺钉坐标:")
            for name, coord in new_coords.items():
                print(f"  {name}: ({coord.x:.3f}, {coord.y:.3f}, {coord.z:.3f})")
            
            return True
        else:
            print("\n❌ 未找到足够的有效坐标点")
            return False
        
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        logging.exception("详细错误信息:")
        return False

def main():
    """主函数"""
    setup_logging()
    
    success = find_valid_coordinates()
    
    if success:
        print("\n🎉 成功找到有效坐标！")
    else:
        print("\n❌ 未能找到有效坐标")
        sys.exit(1)

if __name__ == "__main__":
    main()
