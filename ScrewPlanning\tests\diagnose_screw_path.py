#!/usr/bin/env python3
"""
螺钉路径诊断工具

分析螺钉路径规划结果，检查末端坐标是否合理
"""

import sys
import os
import json
import logging

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def diagnose_screw_path():
    """诊断螺钉路径"""
    try:
        # 导入必要的模块
        from core.bone_density import BoneDensityCalculator
        from utils.geometry import Point3D, Vector3D
        from utils.io_utils import DataLoader
        
        print("=" * 60)
        print("螺钉路径诊断工具")
        print("=" * 60)
        
        # 加载数据
        data_dir = os.path.join(current_dir, 'examples', 'PlanData')
        ct_file = os.path.join(data_dir, 'CT.nii.gz')
        mask_file = os.path.join(data_dir, 'scapula_mask.nii.gz')
        
        loader = DataLoader()
        ct_image = loader.load_ct_image(ct_file)
        mask_image = loader.load_mask_image(mask_file)
        
        # 创建骨密度计算器
        calculator = BoneDensityCalculator(ct_image, mask_image)
        
        # 从规划结果文件读取坐标
        result_file = os.path.join(current_dir, 'top_bottom_result.json')
        if not os.path.exists(result_file):
            print(f"❌ 结果文件不存在: {result_file}")
            return False
        
        with open(result_file, 'r', encoding='utf-8') as f:
            result_data = json.load(f)
        
        print("📊 规划结果分析:")
        print("-" * 60)
        
        # 分析每个螺钉的路径
        if 'screw_endpoints' in result_data:
            for screw_name, endpoint_data in result_data['screw_endpoints'].items():
                endpoint = Point3D(endpoint_data['x'], endpoint_data['y'], endpoint_data['z'])
                
                print(f"\n🔧 螺钉 {screw_name}:")
                print(f"  末端坐标: ({endpoint.x:.2f}, {endpoint.y:.2f}, {endpoint.z:.2f})")
                
                # 检查末端坐标是否在掩膜内
                in_mask = calculator.is_point_in_mask(endpoint)
                print(f"  在肩胛骨掩膜内: {'✅ 是' if in_mask else '❌ 否'}")
                
                if in_mask:
                    # 获取骨密度
                    hu_value = calculator.get_hu_value_at_point(endpoint)
                    bone_density = calculator.get_bone_density_at_point(endpoint)
                    print(f"  HU值: {hu_value:.1f}" if hu_value is not None else "  HU值: 无法获取")
                    print(f"  骨密度: {bone_density:.1f} mg/cc" if bone_density is not None else "  骨密度: 无法获取")
                else:
                    print(f"  ⚠️  末端坐标在肩胛骨外，这可能是问题所在！")
        
        # 分析起始坐标
        print(f"\n📍 起始坐标分析:")
        print("-" * 60)
        
        # 从坐标文件读取起始坐标
        coord_file = os.path.join(data_dir, 'screw_coords_top_bottom.json')
        if os.path.exists(coord_file):
            with open(coord_file, 'r', encoding='utf-8') as f:
                coord_data = json.load(f)
            
            for screw_name, coord_data_item in coord_data.items():
                start_point = Point3D(coord_data_item['x'], coord_data_item['y'], coord_data_item['z'])
                
                print(f"\n螺钉 {screw_name} 起始点:")
                print(f"  坐标: ({start_point.x:.2f}, {start_point.y:.2f}, {start_point.z:.2f})")
                
                # 检查起始点
                in_mask = calculator.is_point_in_mask(start_point)
                print(f"  在掩膜内: {'是' if in_mask else '否'}")
                
                # 如果有末端坐标，计算路径长度
                if 'screw_endpoints' in result_data and screw_name in result_data['screw_endpoints']:
                    endpoint_data = result_data['screw_endpoints'][screw_name]
                    endpoint = Point3D(endpoint_data['x'], endpoint_data['y'], endpoint_data['z'])
                    
                    # 计算实际路径长度
                    import math
                    actual_length = math.sqrt(
                        (endpoint.x - start_point.x)**2 + 
                        (endpoint.y - start_point.y)**2 + 
                        (endpoint.z - start_point.z)**2
                    )
                    print(f"  实际路径长度: {actual_length:.2f} mm")
                    
                    # 计算方向向量
                    direction = Vector3D(
                        endpoint.x - start_point.x,
                        endpoint.y - start_point.y,
                        endpoint.z - start_point.z
                    ).normalize()
                    print(f"  方向向量: ({direction.x:.3f}, {direction.y:.3f}, {direction.z:.3f})")
                    
                    # 沿路径采样检查
                    print(f"  沿路径采样检查:")
                    sample_count = 10
                    in_mask_count = 0
                    total_density = 0
                    valid_density_count = 0
                    
                    for i in range(sample_count + 1):
                        t = i / sample_count
                        sample_point = Point3D(
                            start_point.x + t * (endpoint.x - start_point.x),
                            start_point.y + t * (endpoint.y - start_point.y),
                            start_point.z + t * (endpoint.z - start_point.z)
                        )
                        
                        sample_in_mask = calculator.is_point_in_mask(sample_point)
                        if sample_in_mask:
                            in_mask_count += 1
                            density = calculator.get_bone_density_at_point(sample_point)
                            if density is not None:
                                total_density += density
                                valid_density_count += 1
                    
                    mask_ratio = in_mask_count / (sample_count + 1)
                    avg_density = total_density / max(valid_density_count, 1)
                    
                    print(f"    掩膜内比例: {mask_ratio:.1%}")
                    print(f"    平均骨密度: {avg_density:.1f} mg/cc")
                    
                    if mask_ratio < 0.5:
                        print(f"    ⚠️  路径大部分在掩膜外！")
                    if avg_density < 200:
                        print(f"    ⚠️  平均骨密度较低！")
        
        # 建议
        print(f"\n💡 诊断建议:")
        print("-" * 60)
        
        if 'screw_endpoints' in result_data:
            for screw_name, endpoint_data in result_data['screw_endpoints'].items():
                endpoint = Point3D(endpoint_data['x'], endpoint_data['y'], endpoint_data['z'])
                in_mask = calculator.is_point_in_mask(endpoint)
                
                if not in_mask:
                    print(f"🔧 螺钉 {screw_name}:")
                    print(f"  ❌ 末端坐标在肩胛骨外")
                    print(f"  建议：")
                    print(f"    1. 增加约束角度（当前10°太小）")
                    print(f"    2. 检查螺钉长度设置（当前30mm）")
                    print(f"    3. 调整起始坐标位置")
                    print(f"    4. 降低骨密度阈值")
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断过程中出错: {e}")
        logging.exception("详细错误信息:")
        return False

def main():
    """主函数"""
    setup_logging()
    
    success = diagnose_screw_path()
    
    if success:
        print("\n🎉 诊断完成！")
    else:
        print("\n❌ 诊断失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
