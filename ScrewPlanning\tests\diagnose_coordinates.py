#!/usr/bin/env python3
"""
螺钉坐标诊断工具

检查螺钉坐标是否在肩胛骨掩膜内，以及相关的HU值和骨密度
"""

import sys
import os
import logging

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

def diagnose_coordinates():
    """诊断螺钉坐标"""
    try:
        # 导入必要的模块
        from core.bone_density import BoneDensityCalculator
        from utils.geometry import Point3D
        from utils.io_utils import DataLoader
        
        print("=" * 60)
        print("螺钉坐标诊断工具")
        print("=" * 60)
        
        # 检查数据文件
        data_dir = os.path.join(current_dir, 'examples', 'PlanData')
        ct_file = os.path.join(data_dir, 'CT.nii.gz')
        mask_file = os.path.join(data_dir, 'scapula_mask.nii.gz')
        
        if not os.path.exists(ct_file):
            print(f"❌ CT文件不存在: {ct_file}")
            return False
            
        if not os.path.exists(mask_file):
            print(f"❌ 掩膜文件不存在: {mask_file}")
            return False
        
        print("✅ 数据文件检查通过")
        
        # 加载数据
        print("📂 加载数据...")
        loader = DataLoader()
        ct_image = loader.load_ct_image(ct_file)
        mask_image = loader.load_mask_image(mask_file)
        print("✅ 数据加载成功")
        
        # 创建骨密度计算器
        calculator = BoneDensityCalculator(ct_image, mask_image)
        
        # 定义螺钉坐标（基于示例数据）
        screw_coordinates = {
            'center': Point3D(70.0379, -195.678, 215.651),
            'top': Point3D(69.6298, -190.934, 222.572),
            'bottom': Point3D(70.446, -200.422, 208.731)
        }
        
        print("\n📍 螺钉坐标诊断结果:")
        print("-" * 60)
        
        all_valid = True
        for screw_name, coord in screw_coordinates.items():
            print(f"\n螺钉 {screw_name}:")
            print(f"  坐标: ({coord.x:.3f}, {coord.y:.3f}, {coord.z:.3f})")
            
            # 检查是否在掩膜内
            in_mask = calculator.is_point_in_mask(coord)
            print(f"  在肩胛骨掩膜内: {'✅ 是' if in_mask else '❌ 否'}")
            
            if not in_mask:
                all_valid = False
                continue
            
            # 获取HU值
            hu_value = calculator.get_hu_value_at_point(coord)
            print(f"  HU值: {hu_value:.1f}" if hu_value is not None else "  HU值: 无法获取")
            
            # 获取骨密度
            bone_density = calculator.get_bone_density_at_point(coord)
            print(f"  骨密度: {bone_density:.1f} mg/cc" if bone_density is not None else "  骨密度: 无法获取")
        
        print("\n" + "=" * 60)
        if all_valid:
            print("✅ 所有螺钉坐标都在肩胛骨掩膜内")
        else:
            print("❌ 部分螺钉坐标不在肩胛骨掩膜内")
            print("建议：")
            print("1. 检查坐标系是否正确（物理坐标 vs 图像坐标）")
            print("2. 检查CT图像和掩膜图像的配准是否正确")
            print("3. 尝试调整螺钉坐标到肩胛骨内部")
        
        # 额外信息
        print(f"\n📊 图像信息:")
        print(f"  CT图像尺寸: {calculator.ct_array.shape}")
        print(f"  体素间距: {calculator.spacing}")
        print(f"  图像原点: {calculator.origin}")
        
        # 检查掩膜统计
        if calculator.mask_array is not None:
            mask_volume = (calculator.mask_array > 0).sum()
            total_volume = calculator.mask_array.size
            mask_ratio = mask_volume / total_volume * 100
            print(f"  肩胛骨掩膜体积: {mask_volume} 体素 ({mask_ratio:.1f}%)")
        
        return all_valid
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 诊断过程中出错: {e}")
        logging.exception("详细错误信息:")
        return False

def main():
    """主函数"""
    setup_logging()
    
    success = diagnose_coordinates()
    
    if success:
        print("\n🎉 诊断完成！")
    else:
        print("\n❌ 诊断失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
