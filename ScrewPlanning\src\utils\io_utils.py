"""
文件输入输出工具模块

提供数据加载和结果导出功能
"""

import json
import csv
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging
import SimpleITK as sitk
import vtk

try:
    from .geometry import Point3D, ScrewPath, PlanningResult
except ImportError:
    from utils.geometry import Point3D, ScrewPath, PlanningResult


class DataLoader:
    """数据加载器"""
    
    def __init__(self):
        """初始化数据加载器"""
        logging.info("数据加载器初始化完成")
    
    def load_ct_image(self, file_path: str) -> sitk.Image:
        """
        加载CT图像
        
        Args:
            file_path: CT图像文件路径
            
        Returns:
            SimpleITK图像对象
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"CT图像文件不存在: {file_path}")
        
        try:
            image = sitk.ReadImage(file_path)
            logging.info(f"成功加载CT图像: {file_path}")
            return image
        except Exception as e:
            logging.error(f"加载CT图像失败: {e}")
            raise
    
    def load_mask_image(self, file_path: str) -> Optional[sitk.Image]:
        """
        加载掩膜图像
        
        Args:
            file_path: 掩膜文件路径
            
        Returns:
            SimpleITK掩膜图像对象，如果文件不存在则返回None
        """
        if not os.path.exists(file_path):
            logging.warning(f"掩膜文件不存在: {file_path}")
            return None
        
        try:
            mask = sitk.ReadImage(file_path)
            logging.info(f"成功加载掩膜图像: {file_path}")
            return mask
        except Exception as e:
            logging.error(f"加载掩膜图像失败: {e}")
            return None
    
    def load_reference_points(self, file_path: str) -> List[Point3D]:
        """
        从文件加载参考点
        
        Args:
            file_path: 参考点文件路径（JSON格式）
            
        Returns:
            参考点列表
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"参考点文件不存在: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            points = []
            for point_data in data['reference_points']:
                point = Point3D(point_data['x'], point_data['y'], point_data['z'])
                points.append(point)
            
            logging.info(f"成功加载 {len(points)} 个参考点")
            return points
            
        except Exception as e:
            logging.error(f"加载参考点失败: {e}")
            raise
    
    def load_planning_parameters(self, file_path: str) -> Dict[str, Any]:
        """
        加载规划参数
        
        Args:
            file_path: 参数文件路径（JSON格式）
            
        Returns:
            参数字典
        """
        if not os.path.exists(file_path):
            # 返回默认参数
            return self.get_default_parameters()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                parameters = json.load(f)
            
            logging.info(f"成功加载规划参数: {file_path}")
            return parameters
            
        except Exception as e:
            logging.error(f"加载规划参数失败: {e}")
            return self.get_default_parameters()
    
    def get_default_parameters(self) -> Dict[str, Any]:
        """
        获取默认规划参数

        Returns:
            默认参数字典
        """
        return {
            'constraint_angle': 45.0,
            'screw_length': 50.0,      # 更新为50mm长度
            'screw_radius': 3.25,      # 更新为6.5mm直径（半径3.25mm）
            'radial_resolution': 30,
            'circumferential_resolution': 150,
            'integration_resolution': 30,
            'bone_threshold': 200.0,
            'prosthesis_distance': 12.97
        }


class ResultExporter:
    """结果导出器"""
    
    def __init__(self):
        """初始化结果导出器"""
        logging.info("结果导出器初始化完成")
    
    def export_planning_result(self, result: PlanningResult, 
                             output_dir: str, case_name: str = None) -> str:
        """
        导出规划结果
        
        Args:
            result: 规划结果
            output_dir: 输出目录
            case_name: 病例名称
            
        Returns:
            导出文件路径
        """
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        if case_name is None:
            case_name = f"case_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 创建结果数据
        result_data = {
            'case_name': case_name,
            'timestamp': datetime.now().isoformat(),
            'success': result.success,
            'planning_time': result.planning_time,
            'error_message': result.error_message,
            'statistics': result.statistics,
            'optimal_paths': [],
            'all_candidate_paths': []
        }
        
        # 导出最优路径
        for i, path in enumerate(result.optimal_paths):
            path_data = self._path_to_dict(path, f"optimal_{i+1}")
            result_data['optimal_paths'].append(path_data)
        
        # 导出候选路径（仅前100条以避免文件过大）
        for i, path in enumerate(result.all_candidate_paths[:100]):
            path_data = self._path_to_dict(path, f"candidate_{i+1}")
            result_data['all_candidate_paths'].append(path_data)
        
        # 保存JSON文件
        json_file = os.path.join(output_dir, f"{case_name}_planning_result.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)
        
        logging.info(f"规划结果已导出: {json_file}")
        return json_file
    
    def export_paths_to_csv(self, paths: List[ScrewPath], 
                           output_file: str) -> None:
        """
        将路径数据导出为CSV文件
        
        Args:
            paths: 路径列表
            output_file: 输出文件路径
        """
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow([
                'Path_ID', 'Start_X', 'Start_Y', 'Start_Z',
                'End_X', 'End_Y', 'End_Z', 'Length', 'Radius',
                'Bone_Density_Integral', 'Is_Valid', 'Safety_Score'
            ])
            
            # 写入路径数据
            for i, path in enumerate(paths):
                writer.writerow([
                    i + 1,
                    path.start_point.x, path.start_point.y, path.start_point.z,
                    path.end_point.x, path.end_point.y, path.end_point.z,
                    path.get_path_length(), path.radius,
                    path.bone_density_integral, path.is_valid, path.safety_score
                ])
        
        logging.info(f"路径数据已导出为CSV: {output_file}")
    
    def export_reference_points(self, points: List[Point3D], 
                              output_file: str) -> None:
        """
        导出参考点
        
        Args:
            points: 参考点列表
            output_file: 输出文件路径
        """
        data = {
            'timestamp': datetime.now().isoformat(),
            'reference_points': [
                {'x': p.x, 'y': p.y, 'z': p.z} for p in points
            ]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logging.info(f"参考点已导出: {output_file}")
    
    def export_planning_parameters(self, parameters: Dict[str, Any], 
                                 output_file: str) -> None:
        """
        导出规划参数
        
        Args:
            parameters: 参数字典
            output_file: 输出文件路径
        """
        data = {
            'timestamp': datetime.now().isoformat(),
            'parameters': parameters
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        logging.info(f"规划参数已导出: {output_file}")
    
    def export_vtk_polydata(self, polydata: vtk.vtkPolyData, 
                           output_file: str) -> None:
        """
        导出VTK多边形数据
        
        Args:
            polydata: VTK多边形数据
            output_file: 输出文件路径
        """
        writer = vtk.vtkPolyDataWriter()
        writer.SetFileName(output_file)
        writer.SetInputData(polydata)
        writer.Write()
        
        logging.info(f"VTK数据已导出: {output_file}")
    
    def generate_planning_report(self, result: PlanningResult, 
                               output_file: str, case_name: str = None) -> None:
        """
        生成规划报告
        
        Args:
            result: 规划结果
            output_file: 输出文件路径
            case_name: 病例名称
        """
        if case_name is None:
            case_name = f"Case_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        report_lines = [
            "# 肩盂假体基座螺钉植入路径规划报告",
            "",
            f"**病例名称**: {case_name}",
            f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## 规划结果概览",
            "",
            f"- **规划状态**: {'成功' if result.success else '失败'}",
            f"- **规划耗时**: {result.planning_time:.2f} 秒",
            f"- **候选路径总数**: {result.statistics['total_candidates']}",
            f"- **有效路径数量**: {result.statistics['valid_candidates']}",
            f"- **最优路径数量**: {len(result.optimal_paths)}",
            ""
        ]
        
        if result.error_message:
            report_lines.extend([
                "## 错误信息",
                "",
                f"```",
                result.error_message,
                f"```",
                ""
            ])
        
        if result.optimal_paths:
            report_lines.extend([
                "## 最优路径详情",
                ""
            ])
            
            for i, path in enumerate(result.optimal_paths):
                report_lines.extend([
                    f"### 螺钉 {i+1}",
                    "",
                    f"- **起始点**: ({path.start_point.x:.2f}, {path.start_point.y:.2f}, {path.start_point.z:.2f})",
                    f"- **终止点**: ({path.end_point.x:.2f}, {path.end_point.y:.2f}, {path.end_point.z:.2f})",
                    f"- **路径长度**: {path.get_path_length():.2f} mm",
                    f"- **骨密度积分**: {path.bone_density_integral:.2f}",
                    f"- **安全评分**: {path.safety_score:.2f}",
                    ""
                ])
        
        # 统计信息
        if result.statistics:
            report_lines.extend([
                "## 统计信息",
                "",
                f"- **平均骨密度**: {result.statistics.get('average_bone_density', 0):.2f}",
                f"- **最大骨密度**: {result.statistics.get('max_bone_density', 0):.2f}",
                f"- **最小骨密度**: {result.statistics.get('min_bone_density', 0):.2f}",
                f"- **成功率**: {result.statistics['valid_candidates'] / max(1, result.statistics['total_candidates']) * 100:.1f}%",
                ""
            ])
        
        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        logging.info(f"规划报告已生成: {output_file}")
    
    def _path_to_dict(self, path: ScrewPath, path_id: str) -> Dict[str, Any]:
        """
        将路径对象转换为字典
        
        Args:
            path: 螺钉路径
            path_id: 路径ID
            
        Returns:
            路径字典
        """
        return {
            'id': path_id,
            'start_point': {
                'x': path.start_point.x,
                'y': path.start_point.y,
                'z': path.start_point.z
            },
            'end_point': {
                'x': path.end_point.x,
                'y': path.end_point.y,
                'z': path.end_point.z
            },
            'radius': path.radius,
            'length': path.length,
            'path_length': path.get_path_length(),
            'bone_density_integral': path.bone_density_integral,
            'is_valid': path.is_valid,
            'safety_score': path.safety_score
        }
