"""
几何计算工具模块

提供3D几何计算的基础数据结构和算法
"""

import numpy as np
from typing import List, Tuple, Union
import math


class Point3D:
    """3D点类"""
    
    def __init__(self, x: float = 0.0, y: float = 0.0, z: float = 0.0):
        self.x = float(x)
        self.y = float(y)
        self.z = float(z)
    
    def __str__(self) -> str:
        return f"Point3D({self.x:.3f}, {self.y:.3f}, {self.z:.3f})"
    
    def __repr__(self) -> str:
        return self.__str__()
    
    def __add__(self, other: 'Point3D') -> 'Point3D':
        return Point3D(self.x + other.x, self.y + other.y, self.z + other.z)
    
    def __sub__(self, other: 'Point3D') -> 'Vector3D':
        return Vector3D(self.x - other.x, self.y - other.y, self.z - other.z)
    
    def to_array(self) -> np.ndarray:
        """转换为numpy数组"""
        return np.array([self.x, self.y, self.z])
    
    @classmethod
    def from_array(cls, arr: np.ndarray) -> 'Point3D':
        """从numpy数组创建点"""
        return cls(arr[0], arr[1], arr[2])
    
    def distance_to(self, other: 'Point3D') -> float:
        """计算到另一点的距离"""
        return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2 + (self.z - other.z)**2)


class Vector3D:
    """3D向量类"""
    
    def __init__(self, x: float = 0.0, y: float = 0.0, z: float = 0.0):
        self.x = float(x)
        self.y = float(y)
        self.z = float(z)
    
    def __str__(self) -> str:
        return f"Vector3D({self.x:.3f}, {self.y:.3f}, {self.z:.3f})"
    
    def __repr__(self) -> str:
        return self.__str__()
    
    def __add__(self, other: 'Vector3D') -> 'Vector3D':
        return Vector3D(self.x + other.x, self.y + other.y, self.z + other.z)
    
    def __sub__(self, other: 'Vector3D') -> 'Vector3D':
        return Vector3D(self.x - other.x, self.y - other.y, self.z - other.z)
    
    def __mul__(self, scalar: float) -> 'Vector3D':
        return Vector3D(self.x * scalar, self.y * scalar, self.z * scalar)
    
    def __rmul__(self, scalar: float) -> 'Vector3D':
        return self.__mul__(scalar)
    
    def magnitude(self) -> float:
        """计算向量长度"""
        return math.sqrt(self.x**2 + self.y**2 + self.z**2)
    
    def normalize(self) -> 'Vector3D':
        """归一化向量"""
        mag = self.magnitude()
        if mag == 0:
            return Vector3D(0, 0, 0)
        return Vector3D(self.x / mag, self.y / mag, self.z / mag)
    
    def dot(self, other: 'Vector3D') -> float:
        """点积"""
        return self.x * other.x + self.y * other.y + self.z * other.z
    
    def cross(self, other: 'Vector3D') -> 'Vector3D':
        """叉积"""
        return Vector3D(
            self.y * other.z - self.z * other.y,
            self.z * other.x - self.x * other.z,
            self.x * other.y - self.y * other.x
        )
    
    def to_array(self) -> np.ndarray:
        """转换为numpy数组"""
        return np.array([self.x, self.y, self.z])
    
    @classmethod
    def from_array(cls, arr: np.ndarray) -> 'Vector3D':
        """从numpy数组创建向量"""
        return cls(arr[0], arr[1], arr[2])


class RotationMatrix:
    """3D旋转矩阵类"""
    
    def __init__(self, matrix: np.ndarray = None):
        if matrix is None:
            self.matrix = np.eye(3)
        else:
            self.matrix = np.array(matrix)
    
    @classmethod
    def from_axis_angle(cls, axis: Vector3D, angle: float) -> 'RotationMatrix':
        """从轴角表示创建旋转矩阵"""
        axis_normalized = axis.normalize()
        u = axis_normalized.to_array()
        cos_theta = math.cos(angle)
        sin_theta = math.sin(angle)
        
        # Rodrigues旋转公式
        matrix = cos_theta * np.eye(3) + sin_theta * np.array([
            [0, -u[2], u[1]],
            [u[2], 0, -u[0]],
            [-u[1], u[0], 0]
        ]) + (1 - cos_theta) * np.outer(u, u)
        
        return cls(matrix)
    
    def apply_to_vector(self, vector: Vector3D) -> Vector3D:
        """将旋转应用到向量"""
        result = self.matrix @ vector.to_array()
        return Vector3D.from_array(result)
    
    def apply_to_point(self, point: Point3D) -> Point3D:
        """将旋转应用到点"""
        result = self.matrix @ point.to_array()
        return Point3D.from_array(result)


class ScrewPath:
    """螺钉路径类"""

    def __init__(self, start_point: Point3D, end_point: Point3D,
                 radius: float = 1.65, length: float = 38.97):
        self.start_point = start_point
        self.end_point = end_point
        self.radius = radius  # 螺钉半径 (mm)
        self.length = length  # 螺钉长度 (mm)
        self.bone_density_integral = 0.0
        self.is_valid = True
        self.safety_score = 0.0
        self.coverage_ratio = 0.0  # 体积覆盖率 (0.0 - 1.0)
    
    def get_direction_vector(self) -> Vector3D:
        """获取路径方向向量"""
        return (self.end_point - self.start_point).normalize()
    
    def get_path_length(self) -> float:
        """获取路径长度"""
        return self.start_point.distance_to(self.end_point)
    
    def get_points_along_path(self, num_points: int) -> List[Point3D]:
        """获取路径上的采样点"""
        points = []
        for i in range(num_points):
            t = i / (num_points - 1) if num_points > 1 else 0
            x = self.start_point.x + t * (self.end_point.x - self.start_point.x)
            y = self.start_point.y + t * (self.end_point.y - self.start_point.y)
            z = self.start_point.z + t * (self.end_point.z - self.start_point.z)
            points.append(Point3D(x, y, z))
        return points


class ConeSpace:
    """锥形空间类"""
    
    def __init__(self, apex: Point3D, axis: Vector3D, height: float, 
                 base_radius: float, constraint_angle: float = 45.0):
        self.apex = apex
        self.axis = axis.normalize()
        self.height = height
        self.base_radius = base_radius
        self.constraint_angle = math.radians(constraint_angle)
    
    def get_base_center(self) -> Point3D:
        """获取锥底中心点"""
        base_center_array = self.apex.to_array() + self.height * self.axis.to_array()
        return Point3D.from_array(base_center_array)
    
    def is_point_inside(self, point: Point3D) -> bool:
        """判断点是否在锥形空间内"""
        apex_to_point = point - self.apex
        projection_length = apex_to_point.dot(self.axis)
        
        if projection_length < 0 or projection_length > self.height:
            return False
        
        # 计算点到轴线的距离
        projection_point_array = self.apex.to_array() + projection_length * self.axis.to_array()
        projection_point = Point3D.from_array(projection_point_array)
        distance_to_axis = point.distance_to(projection_point)
        
        # 在该高度处的锥半径
        radius_at_height = self.base_radius * (projection_length / self.height)
        
        return distance_to_axis <= radius_at_height


class PlanningResult:
    """规划结果类"""
    
    def __init__(self):
        self.optimal_paths: List[ScrewPath] = []
        self.all_candidate_paths: List[ScrewPath] = []
        self.planning_time: float = 0.0
        self.success: bool = False
        self.error_message: str = ""
        self.statistics = {
            'total_candidates': 0,
            'valid_candidates': 0,
            'average_bone_density': 0.0,
            'max_bone_density': 0.0,
            'min_bone_density': 0.0
        }
        # 用于路径可视化的有效路径端点
        self.valid_path_endpoints: List[Tuple['Point3D', 'Point3D']] = []
        # Pareto最优解（当使用Pareto优化时）
        self.pareto_solutions = None
    
    def add_optimal_path(self, path: ScrewPath):
        """添加最优路径"""
        self.optimal_paths.append(path)
    
    def get_best_path(self) -> ScrewPath:
        """获取最佳路径"""
        if not self.optimal_paths:
            return None
        return max(self.optimal_paths, key=lambda p: p.bone_density_integral)
