import base64
import ctypes
import hashlib
import json
import os
import shutil
import tempfile
import vtk
import zipfile

from nacl.bindings import crypto_secretbox_open
from nacl.exceptions import CryptoError

key = b'ESTUN'
fixDiskFileName = 'PLANPROSTHESISDATA_ScapulaPlan_fixDisk'
stalkFileName = 'PLANPROSTHESISDATA_HumerusPlan_stalk'

def writeStlFile(fileName, polydata):
    if not os.path.exists(os.path.dirname(fileName)):
        os.makedirs(os.path.dirname(fileName))
    writer = vtk.vtkSTLWriter()
    writer.SetFileName(fileName)
    writer.SetInputData(polydata)
    writer.Write()

def findFirstFile(directory, fileFullName):
    filePath = ''
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file == fileFullName:
                filePath = os.path.join(root, file)
                break
    return filePath

def decrypt(ciphertext_b64: str, key_str: str, nonce_b64: str) -> str:
    # Decode Base64 strings to bytes
    ciphertext = base64.b64decode(ciphertext_b64)
    nonce = base64.b64decode(nonce_b64)
    
    # Derive 32-byte key using BLAKE2b
    key = hashlib.blake2b(
        key_str,
        digest_size=32  # crypto_secretbox_KEYBYTES (32 bytes)
    ).digest()
    
    try:
        # Decrypt using libsodium's crypto_secretbox_open
        plaintext_bytes = crypto_secretbox_open(ciphertext, nonce, key)
        return plaintext_bytes.decode('utf-8')
    except CryptoError:
        # Handle decryption failure (e.g., invalid key/ciphertext)
        return ""

def readJsonFile(filePath):
    try:
        with open(filePath, 'r', encoding='utf-8') as file:
            return json.load(file)
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def readJsonPolyData(filePath):
    fixDiskJsonObj = readJsonFile(filePath)
    nonce = fixDiskJsonObj["Nonce"]
    ciphertext = fixDiskJsonObj["PolyData"]
    matrixArr = fixDiskJsonObj["MatrixPrs2I"]

    decrypted = decrypt(ciphertext, key, nonce)
    reader = vtk.vtkXMLPolyDataReader()
    reader.SetInputString(decrypted)
    reader.SetReadFromInputString(True)
    reader.Update()

    matrix = vtk.vtkMatrix4x4()
    for i in range(4):
        for j in range(4):
            matrix.SetElement(i, j, matrixArr[i][j])

    transform = vtk.vtkTransform()
    transform.SetMatrix(matrix)
    transform.Update()
    transformFilter = vtk.vtkTransformPolyDataFilter()
    transformFilter.SetInputData(reader.GetOutput())
    transformFilter.SetTransform(transform)
    transformFilter.Update()

    return transformFilter.GetOutput()

if __name__ == "__main__":
    tempDir = ''
    try:
        outputDir = r"F:\ESTUN\data\20250619\Clinical_PreOpt\YAN_SHUANG_GEN\ExportOptPlan"
        outputFixDiskFileName = r"Glenoid_Baseplate.stl"
        outputStalkFileName = r"Humerus_Stem.stl"
        
        # tempDir = extractOpp2Temp(oppFilePath)
        tempDir = r"F:\ESTUN\data\20250619\Clinical_PreOpt\YAN_SHUANG_GEN\YAN SHUANG GEN_20250630135030"
        fixDiskFilePath = findFirstFile(tempDir, f'{fixDiskFileName}.json')
        stalkFilePath = findFirstFile(tempDir, f'{stalkFileName}.json')

        outputFixDiskFilePath = f'{outputDir}\\{outputFixDiskFileName}'
        fixDiskPolyData = readJsonPolyData(fixDiskFilePath)
        writeStlFile(outputFixDiskFilePath, fixDiskPolyData)
        print(f"已成功导出: {outputFixDiskFilePath}")

        outputStalkFilePath = f'{outputDir}\\{outputStalkFileName}'
        stalkPolyData = readJsonPolyData(stalkFilePath)
        writeStlFile(outputStalkFilePath, stalkPolyData)
        print(f"已成功导出: {outputStalkFilePath}")
        
    except Exception as e:
        print(f"Error: {e}")