#!/usr/bin/env python3
"""
测试改进的基座方向计算算法
"""

import sys
import os
import math

# 添加src目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

from utils.geometry import Point3D, Vector3D


def test_improved_direction_calculation():
    """测试改进的方向计算方法"""
    
    print("=" * 80)
    print("测试改进的基座方向计算算法")
    print("=" * 80)
    
    # 使用case02的坐标数据
    screw_coordinates = {
        'top': Point3D(-68.3425, 191.373, 222.918),
        'bottom': Point3D(-69.1684, 200.974, 208.913),
        'center': Point3D(-62.2423, 198.69, 217.256)
    }
    
    center = screw_coordinates['center']
    top = screw_coordinates['top']
    bottom = screw_coordinates['bottom']
    
    print("📍 螺钉坐标:")
    print(f"  上螺钉 (top):    {top}")
    print(f"  下螺钉 (bottom): {bottom}")
    print(f"  中螺钉 (center): {center}")
    print()
    
    # 原始方法
    print("🔄 原始三点方法:")
    print("-" * 50)
    
    # 原始算法
    vector1_old = top - center
    vector2_old = bottom - center
    normal_old = vector1_old.cross(vector2_old).normalize()
    base_center_old = center
    
    print(f"  基座中心: {base_center_old}")
    print(f"  法向量:   {normal_old}")
    print()
    
    # 改进方法
    print("✨ 改进的三点方法:")
    print("-" * 50)
    
    # 步骤1：计算三个点形成的三角平面的法向量
    vector1 = top - center
    vector2 = bottom - center
    triangle_normal = vector1.cross(vector2).normalize()
    
    print(f"  步骤1 - 三角平面法向量: {triangle_normal}")
    
    # 步骤2：计算上下螺钉连线向量
    screw_line_vector = top - bottom
    
    print(f"  步骤2 - 上下螺钉向量:   {screw_line_vector}")
    print(f"           (长度: {screw_line_vector.magnitude():.3f} mm)")
    
    # 步骤3：计算基座平面法向量（垂直于三角平面，也垂直于上下螺钉连线）
    normal_new = triangle_normal.cross(screw_line_vector).normalize()
    
    # 步骤4：基座中心为上下螺钉的中点
    base_center_new = Point3D(
        (top.x + bottom.x) / 2,
        (top.y + bottom.y) / 2,
        (top.z + bottom.z) / 2
    )
    
    print(f"  步骤3 - 基座平面法向量: {normal_new}")
    print(f"  步骤4 - 基座中心:       {base_center_new}")
    print()
    
    # 分析差异
    print("📊 方法对比分析:")
    print("-" * 50)
    
    # 基座中心差异
    center_diff = base_center_new - base_center_old
    center_distance = center_diff.magnitude()
    
    print(f"  基座中心位移: {center_diff}")
    print(f"  位移距离:     {center_distance:.3f} mm")
    
    # 法向量差异
    angle_diff = math.acos(max(-1, min(1, normal_old.dot(normal_new))))
    angle_diff_degrees = math.degrees(angle_diff)
    
    print(f"  法向量夹角:   {angle_diff_degrees:.2f}°")
    
    # 验证几何关系
    print()
    print("🔍 几何关系验证:")
    print("-" * 50)
    
    # 验证基座平面确实过上下螺钉
    top_to_center = top - base_center_new
    bottom_to_center = bottom - base_center_new
    
    # 这两个向量应该都垂直于基座法向量
    dot1 = top_to_center.dot(normal_new)
    dot2 = bottom_to_center.dot(normal_new)
    
    print(f"  上螺钉到基座中心向量与法向量点积: {dot1:.6f} (应接近0)")
    print(f"  下螺钉到基座中心向量与法向量点积: {dot2:.6f} (应接近0)")
    
    # 验证基座法向量垂直于上下螺钉连线
    dot3 = screw_line_vector.normalize().dot(normal_new)
    print(f"  上下螺钉向量与基座法向量点积:     {dot3:.6f} (应接近0)")
    
    # 验证基座法向量垂直于三角平面法向量
    dot4 = triangle_normal.dot(normal_new)
    print(f"  三角平面法向量与基座法向量点积:   {dot4:.6f} (应接近0)")
    
    print()
    print("✅ 测试完成！")
    
    return {
        'old_method': {
            'base_center': base_center_old,
            'normal': normal_old
        },
        'new_method': {
            'base_center': base_center_new,
            'normal': normal_new
        },
        'differences': {
            'center_distance': center_distance,
            'angle_difference': angle_diff_degrees
        }
    }


if __name__ == "__main__":
    test_improved_direction_calculation()
