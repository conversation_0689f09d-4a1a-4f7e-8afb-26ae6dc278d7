#!/usr/bin/env python3
"""
肩盂假体基座螺钉植入路径规划系统启动脚本

注意：图形用户界面已被移除
请使用编程接口或示例脚本来运行规划算法

使用方法:
    python run_screw_planning.py  # 显示使用说明
    python examples/basic_planning_example.py  # 运行基本示例
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def main():
    """主函数"""
    print("=" * 60)
    print("肩盂假体基座螺钉植入路径规划系统")
    print("Screw Planning System for Reverse Shoulder Arthroplasty")
    print("=" * 60)
    print()

    # 检查依赖并显示使用说明
    try:
        # 导入并启动主程序（现在只显示使用说明）
        from main import main as start_application
        start_application()

    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
