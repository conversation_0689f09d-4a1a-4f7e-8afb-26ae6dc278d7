#!/usr/bin/env python3
"""
业务螺钉路径规划启动脚本

基于实际业务需求的螺钉路径规划工具
支持3个螺钉起始坐标输入，返回螺钉末端坐标
"""

import sys
import os

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)

def main():
    """主函数"""
    try:
        # 导入并启动业务规划CLI
        from cli.business_cli import main as start_business_cli
        start_business_cli()
    except ImportError as e:
        print(f"导入失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
