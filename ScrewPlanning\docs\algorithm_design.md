# 算法设计文档

## 概述

本文档详细描述了肩盂假体基座螺钉植入路径规划系统的核心算法设计，该系统基于Li等人2022年发表的论文《Automatic surgical planning based on bone density assessment and path integral in cone space for reverse shoulder arthroplasty》实现。

## 核心算法架构

### 1. 交互式操作模块

#### 1.1 参考点选择
系统需要用户选择4个关键参考点：
- **P1**: 假体基座中心点
- **P2**: 平面参考点1  
- **P3**: 平面参考点2
- **P4**: 方向参考点

#### 1.2 假体定位计算
根据参考点计算假体的位置和方向：

```
# 计算假体平面法向量
v1 = P2 - P1
v2 = P3 - P1  
ε = normalize(v1 × v2)

# 计算方向向量
m = normalize(P4 - P1)

# 计算螺钉起始点
ρ = 12.97  # 螺钉孔间距
P5 = P1 + (ρ/2) * m  # 螺钉1起始点
P6 = P1 - (ρ/2) * m  # 螺钉2起始点
```

### 2. 锥形空间生成模块

#### 2.1 锥形空间参数计算
基于约束角度α和螺钉几何参数生成搜索空间：

```
# 偏移参数
η = l * sin(α)

# 锥轴方向（带偏移避免干涉）
n1 = normalize(l*ε + η*m)  # 螺钉1锥轴
n2 = normalize(l*ε - η*m)  # 螺钉2锥轴

# 锥底半径
r_c = l * tan(α)
```

#### 2.2 候选路径生成
在锥形空间内均匀分布生成候选路径：

```
# 径向和周向采样
for r_idx in range(R_l + 1):
    radius = r_c * r_idx / R_l
    
    for theta_idx in range(R_a):
        theta = 2π * theta_idx / R_a
        
        # 锥底面采样点
        end_point = base_center + radius * (cos(theta)*e1 + sin(theta)*e2)
        
        # 创建候选路径
        path = ScrewPath(apex, end_point)
```

### 3. 骨密度评估模块

#### 3.1 公式法骨密度计算
使用经验公式将HU值转换为骨密度：

```
QCT = 17.8 + 0.7 × HU
```

#### 3.2 路径积分计算
沿候选路径计算骨密度积分：

```
# 路径采样
sample_points = get_points_along_path(path, R_i)

# 骨密度积分
integral = 0
for point in sample_points:
    hu_value = get_hu_at_point(point)
    bone_density = 17.8 + 0.7 * hu_value
    integral += bone_density

# 归一化积分值
path.bone_density_integral = integral * path_length / R_i
```

### 4. 体积约束检查模块

#### 4.1 螺钉表面采样
在螺钉表面生成检查点以验证是否暴露：

```
# 围绕路径生成表面点
for angle in range(0, 2π, 2π/angular_resolution):
    for t in range(0, 1, 1/length_resolution):
        # 路径上的点
        path_point = start + t * (end - start)
        
        # 螺钉表面的点
        offset = radius * (cos(angle)*perp1 + sin(angle)*perp2)
        surface_point = path_point + offset
        
        # 检查HU值
        if get_hu_at_point(surface_point) < bone_threshold:
            return False  # 螺钉暴露
```

### 5. 路径优化模块

#### 5.1 最优解选择
选择骨密度积分最大且满足约束的路径：

```
# 过滤有效路径
valid_paths = [path for path in candidates if path.is_valid]

# 分别为两个螺钉选择最优路径
best_path1 = max(valid_paths1, key=lambda p: p.bone_density_integral)
best_path2 = max(valid_paths2, key=lambda p: p.bone_density_integral)
```

#### 5.2 角度约束验证
确保螺钉间夹角满足约束条件：

```
angle = acos(|dir1 · dir2|)
if angle > constraint_angle:
    # 路径不满足角度约束
    reject_path()
```

## 算法参数

### 默认参数设置
- **约束角度 (α)**: 45°
- **螺钉长度 (l)**: 38.97 mm
- **螺钉半径 (r)**: 1.65 mm
- **径向分辨率 (R_l)**: 30
- **周向分辨率 (R_a)**: 150
- **积分分辨率 (R_i)**: 30
- **骨骼阈值**: 200 HU

### 性能优化策略
1. **并行计算**: 候选路径评估可并行化
2. **早期终止**: 不满足体积约束的路径立即丢弃
3. **内存优化**: 分批处理大量候选路径
4. **缓存机制**: 缓存重复计算的结果

## 算法复杂度分析

### 时间复杂度
- **候选路径生成**: O(R_a × R_l)
- **路径评估**: O(N_paths × R_i)
- **体积约束检查**: O(N_paths × N_surface_points)
- **总体复杂度**: O(R_a × R_l × R_i)

### 空间复杂度
- **候选路径存储**: O(R_a × R_l)
- **CT图像数据**: O(W × H × D)
- **总体空间复杂度**: O(R_a × R_l + W × H × D)

## 算法验证

### 验证指标
1. **规划成功率**: 有效路径数量 / 总候选路径数量
2. **角度约束满足率**: 满足角度约束的路径比例
3. **骨密度积分值**: 最优路径的骨密度积分
4. **计算效率**: 规划算法执行时间

### 测试用例
- **正常病例**: 骨质正常的肩胛骨
- **骨质疏松病例**: 低骨密度的肩胛骨
- **解剖变异病例**: 异常解剖结构的肩胛骨

## 算法局限性

1. **假设条件**: 假设螺钉为刚性直线路径
2. **简化模型**: 未考虑软组织和血管结构
3. **静态规划**: 未考虑手术过程中的动态因素
4. **经验公式**: 骨密度计算基于统计经验公式

## 未来改进方向

1. **动态规划**: 考虑手术过程中的实时反馈
2. **多目标优化**: 同时优化多个临床指标
3. **机器学习**: 基于历史数据优化参数选择
4. **个性化定制**: 根据患者特征调整算法参数
