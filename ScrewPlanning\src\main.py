"""
肩盂假体基座螺钉植入路径规划系统命令行工具

基于骨密度评估和锥形空间路径积分的自动手术规划系统
注意：UI界面已被移除，请使用编程接口或示例脚本
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, current_dir)  # 添加src目录到路径


def setup_logging():
    """设置日志配置"""
    log_dir = os.path.join(project_root, 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file = os.path.join(log_dir, f'screw_planning_{datetime.now().strftime("%Y%m%d")}.log')

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    logging.info("=" * 60)
    logging.info("肩盂假体基座螺钉植入路径规划系统")
    logging.info("=" * 60)


def show_usage():
    """显示使用说明"""
    print("=" * 60)
    print("肩盂假体基座螺钉植入路径规划系统")
    print("Screw Planning System for Reverse Shoulder Arthroplasty")
    print("=" * 60)
    print()
    print("注意：图形用户界面已被移除")
    print("请使用以下方式运行系统：")
    print()
    print("1. 使用业务规划接口：")
    print("   python src/cli/business_cli.py --help")
    print("   # 或使用编程接口：")
    print("   from core.business_planning import BusinessPathPlanner")
    print()
    print("2. 运行示例脚本：")
    print("   python examples/basic_planning_example.py")
    print("   python examples/real_data_planning_example.py")
    print()
    print("3. 查看文档：")
    print("   README.md - 项目概述")
    print("   docs/user_manual.md - 用户手册")
    print("   docs/algorithm_design.md - 算法设计")
    print()
    print("核心功能模块：")
    print("- core/business_planning.py - 业务路径规划主算法")
    print("- core/bone_density.py - 骨密度计算")
    print("- core/cone_space.py - 锥形空间生成")
    print("- utils/geometry.py - 几何计算工具")
    print("- utils/io_utils.py - 数据输入输出")
    print("=" * 60)


def main():
    """主函数"""
    # 设置日志
    setup_logging()

    # 显示使用说明
    show_usage()

    logging.info("系统信息显示完成")
    logging.info("请使用编程接口或示例脚本来运行规划算法")


if __name__ == "__main__":
    main()
