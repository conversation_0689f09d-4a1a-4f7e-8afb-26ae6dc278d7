# 快速启动指南

## 🚀 5分钟快速体验

### 第一步：环境检查
```bash
# 检查Python版本（需要3.8+）
python --version

# 检查是否在项目根目录
ls ScrewPlanning/
```

### 第二步：验证系统功能
```bash
# 运行系统验证脚本
python ScrewPlanning/test_system.py
```

期望输出：
```
🎉 所有测试通过！系统功能正常
```

### 第三步：运行示例
```bash
# 运行基本规划示例
python ScrewPlanning/examples/basic_planning_example.py
```

期望输出：
```
✅ 示例执行成功！
规划状态: 成功
规划耗时: 0.51 秒
最优路径数量: 1
```

### 第四步：启动图形界面（可选）
```bash
# 启动完整的图形界面系统
python ScrewPlanning/run_screw_planning.py
```

## 📋 系统要求

### 最低要求
- **Python**: 3.8+
- **内存**: 4GB RAM
- **存储**: 1GB 可用空间
- **显卡**: 支持OpenGL 2.0

### 推荐配置
- **Python**: 3.9+
- **内存**: 8GB+ RAM
- **存储**: 2GB+ 可用空间
- **显卡**: 支持OpenGL 3.0+的独立显卡

## 🔧 依赖安装

### 自动安装（推荐）
```bash
# 安装所有依赖
pip install -r requirements.txt
```

### 手动安装
```bash
# 核心依赖
pip install PyQt5 vtk SimpleITK numpy scipy scikit-image

# 可选依赖
pip install matplotlib pandas
```

## 📁 项目结构

```
ScrewPlanning/
├── 🚀 run_screw_planning.py    # 启动脚本
├── 🧪 test_system.py           # 系统验证
├── 📖 README.md                # 项目说明
├── 📊 PROJECT_SUMMARY.md       # 项目总结
├── src/                        # 源代码
├── docs/                       # 文档
├── examples/                   # 示例
└── tests/                      # 测试
```

## 🎯 主要功能

### 1. 核心算法
- ✅ 锥形空间路径生成
- ✅ 骨密度积分计算
- ✅ 体积约束优化
- ✅ 多参数路径规划

### 2. 用户界面
- ✅ 3D可视化显示
- ✅ 交互式参考点选择
- ✅ 实时参数调整
- ✅ 规划结果展示

### 3. 数据处理
- ✅ CT图像加载（NIfTI格式）
- ✅ 骨骼掩膜处理
- ✅ 结果导出（JSON/CSV/MD）
- ✅ 3D模型生成

## 🔍 故障排除

### 常见问题

#### Q1: 导入错误
```
ImportError: No module named 'PyQt5'
```
**解决方案**：
```bash
pip install PyQt5
```

#### Q2: VTK显示问题
```
VTK rendering error
```
**解决方案**：
1. 更新显卡驱动
2. 检查OpenGL支持
3. 尝试软件渲染模式

#### Q3: 内存不足
```
MemoryError: Unable to allocate array
```
**解决方案**：
1. 降低图像分辨率
2. 减少规划参数
3. 增加系统内存

#### Q4: 规划失败
```
规划状态: 失败
没有找到有效的螺钉路径
```
**解决方案**：
1. 检查参考点选择是否正确
2. 调整骨骼阈值参数
3. 降低约束角度

### 调试模式
```bash
# 启用详细日志
export PYTHONPATH=ScrewPlanning/src
python -c "import logging; logging.basicConfig(level=logging.DEBUG)"
python ScrewPlanning/test_system.py
```

## 📚 学习资源

### 文档阅读顺序
1. **README.md** - 项目概述
2. **QUICK_START.md** - 快速开始（当前文档）
3. **docs/user_manual.md** - 详细用户手册
4. **docs/algorithm_design.md** - 算法技术细节
5. **PROJECT_SUMMARY.md** - 项目总结

### 示例学习
1. **test_system.py** - 了解系统功能
2. **examples/basic_planning_example.py** - 学习API使用
3. **src/main.py** - 了解图形界面

## 🎮 交互式教程

### 步骤1：验证安装
```bash
python ScrewPlanning/test_system.py
```

### 步骤2：运行示例
```bash
python ScrewPlanning/examples/basic_planning_example.py
```

### 步骤3：查看结果
```bash
# 查看生成的报告
cat ScrewPlanning/examples/output/example_planning_report.md
```

### 步骤4：启动GUI（可选）
```bash
python ScrewPlanning/run_screw_planning.py
```

## 🔗 相关链接

- **参考论文**: Li et al. (2022) - Automatic surgical planning based on bone density assessment and path integral in cone space for reverse shoulder arthroplasty
- **骨密度计算项目**: ../README.md
- **技术文档**: docs/
- **示例代码**: examples/

## 💡 使用技巧

### 1. 参数调优
- **快速测试**: 降低分辨率参数
- **精确规划**: 提高分辨率参数
- **骨质疏松**: 降低骨骼阈值
- **高骨密度**: 提高骨骼阈值

### 2. 性能优化
- 使用较小的CT图像进行测试
- 关闭不必要的可视化效果
- 定期清理临时文件

### 3. 结果分析
- 查看骨密度积分值判断路径质量
- 检查角度约束是否满足
- 验证螺钉是否暴露

## 🆘 获取帮助

### 自助解决
1. 查看错误日志
2. 运行系统验证脚本
3. 检查文档和FAQ

### 问题反馈
提供以下信息：
- 操作系统和Python版本
- 完整的错误信息
- 重现问题的步骤
- 使用的数据文件信息

---

## 🎉 开始使用

现在您已经了解了基本信息，可以开始使用系统了！

```bash
# 一键验证和体验
python ScrewPlanning/test_system.py && python ScrewPlanning/examples/basic_planning_example.py
```

祝您使用愉快！ 🚀
