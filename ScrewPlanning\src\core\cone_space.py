"""
锥形空间生成模块

基于论文算法生成螺钉路径搜索的锥形空间
"""

import numpy as np
import math
from typing import List, Tuple
import logging

try:
    from ..utils.geometry import Point3D, Vector3D, RotationMatrix, ScrewPath, ConeSpace
except ImportError:
    from utils.geometry import Point3D, Vector3D, RotationMatrix, ScrewPath, ConeSpace


class ConeSpaceGenerator:
    """锥形空间生成器"""
    
    def __init__(self, constraint_angle: float = 45.0, 
                 screw_length: float = 38.97,
                 screw_radius: float = 1.65):
        """
        初始化锥形空间生成器
        
        Args:
            constraint_angle: 约束角度（度）
            screw_length: 螺钉长度 (mm)
            screw_radius: 螺钉半径 (mm)
        """
        self.constraint_angle = math.radians(constraint_angle)
        self.screw_length = screw_length
        self.screw_radius = screw_radius
        
        logging.info(f"锥形空间生成器初始化完成")
        logging.info(f"约束角度: {constraint_angle}°")
        logging.info(f"螺钉长度: {screw_length}mm")
        logging.info(f"螺钉半径: {screw_radius}mm")
    
    def create_rotation_matrix(self, axis: Vector3D, angle: float) -> np.ndarray:
        """
        创建绕指定轴旋转的旋转矩阵
        
        Args:
            axis: 旋转轴（单位向量）
            angle: 旋转角度（弧度）
            
        Returns:
            3x3旋转矩阵
        """
        axis_normalized = axis.normalize()
        u = axis_normalized.to_array()
        cos_theta = math.cos(angle)
        sin_theta = math.sin(angle)
        
        # Rodrigues旋转公式
        matrix = cos_theta * np.eye(3) + sin_theta * np.array([
            [0, -u[2], u[1]],
            [u[2], 0, -u[0]],
            [-u[1], u[0], 0]
        ]) + (1 - cos_theta) * np.outer(u, u)
        
        return matrix
    
    def generate_cone_space(self, apex: Point3D, axis_direction: Vector3D) -> ConeSpace:
        """
        生成锥形空间
        
        Args:
            apex: 锥顶点（螺钉起始点）
            axis_direction: 锥轴方向
            
        Returns:
            锥形空间对象
        """
        # 计算锥底半径
        base_radius = self.screw_length * math.tan(self.constraint_angle)
        
        cone_space = ConeSpace(
            apex=apex,
            axis=axis_direction,
            height=self.screw_length,
            base_radius=base_radius,
            constraint_angle=math.degrees(self.constraint_angle)
        )
        
        return cone_space
    
    def generate_candidate_paths(self, cone_space: ConeSpace,
                               radial_resolution: int = 30,
                               circumferential_resolution: int = 150) -> List[ScrewPath]:
        """
        在锥形空间内生成候选路径
        
        Args:
            cone_space: 锥形空间
            radial_resolution: 径向分辨率
            circumferential_resolution: 周向分辨率
            
        Returns:
            候选路径列表
        """
        candidate_paths = []
        
        # 获取锥底中心
        base_center = cone_space.get_base_center()
        
        # 创建垂直于锥轴的两个正交向量
        axis = cone_space.axis
        if abs(axis.x) < 0.9:
            temp_vector = Vector3D(1, 0, 0)
        else:
            temp_vector = Vector3D(0, 1, 0)
        
        perpendicular1 = axis.cross(temp_vector).normalize()
        perpendicular2 = axis.cross(perpendicular1).normalize()
        
        # 在锥底面生成均匀分布的点
        for r_idx in range(radial_resolution + 1):
            # 径向距离（从0到base_radius）
            if radial_resolution == 0:
                radius = 0
            else:
                radius = cone_space.base_radius * r_idx / radial_resolution
            
            # 对于中心点，只生成一个路径
            if radius == 0:
                end_point = base_center
                path = ScrewPath(
                    start_point=cone_space.apex,
                    end_point=end_point,
                    radius=self.screw_radius,
                    length=self.screw_length
                )
                candidate_paths.append(path)
                continue
            
            # 周向角度分布
            for theta_idx in range(circumferential_resolution):
                theta = 2 * math.pi * theta_idx / circumferential_resolution
                
                # 计算锥底面上的点
                offset_vector = (perpendicular1 * math.cos(theta) + 
                               perpendicular2 * math.sin(theta)) * radius
                
                end_point_array = base_center.to_array() + offset_vector.to_array()
                end_point = Point3D.from_array(end_point_array)
                
                # 创建候选路径
                path = ScrewPath(
                    start_point=cone_space.apex,
                    end_point=end_point,
                    radius=self.screw_radius,
                    length=self.screw_length
                )
                
                candidate_paths.append(path)
        
        logging.info(f"生成候选路径数量: {len(candidate_paths)}")
        return candidate_paths
    
    def generate_paths_for_screw_pair(self, p1: Point3D, p4: Point3D, 
                                    epsilon: Vector3D, m: Vector3D,
                                    rho: float = 12.97,
                                    radial_resolution: int = 30,
                                    circumferential_resolution: int = 150) -> Tuple[List[ScrewPath], List[ScrewPath]]:
        """
        为一对螺钉生成候选路径
        
        Args:
            p1: 假体基座中心点
            p4: 方向参考点
            epsilon: 假体法向量
            m: 螺钉方向向量
            rho: 螺钉孔间距 (mm)
            radial_resolution: 径向分辨率
            circumferential_resolution: 周向分辨率
            
        Returns:
            (螺钉1路径列表, 螺钉2路径列表)
        """
        # 计算两个螺钉的起始点
        offset = m * (rho / 2)
        p5 = Point3D.from_array(p1.to_array() + offset.to_array())  # 螺钉1起始点
        p6 = Point3D.from_array(p1.to_array() - offset.to_array())  # 螺钉2起始点
        
        # 计算偏移参数
        eta = self.screw_length * math.sin(self.constraint_angle)
        
        # 计算两个螺钉的锥轴方向（带偏移以避免干涉）
        n1 = (epsilon * self.screw_length + m * eta).normalize()
        n2 = (epsilon * self.screw_length - m * eta).normalize()
        
        # 生成两个锥形空间
        cone_space1 = self.generate_cone_space(p5, n1)
        cone_space2 = self.generate_cone_space(p6, n2)
        
        # 生成候选路径
        paths1 = self.generate_candidate_paths(
            cone_space1, radial_resolution, circumferential_resolution)
        paths2 = self.generate_candidate_paths(
            cone_space2, radial_resolution, circumferential_resolution)
        
        logging.info(f"螺钉1候选路径: {len(paths1)}")
        logging.info(f"螺钉2候选路径: {len(paths2)}")
        
        return paths1, paths2
    
    def filter_paths_by_angle_constraint(self, paths1: List[ScrewPath], 
                                       paths2: List[ScrewPath],
                                       max_angle: float = 45.0) -> Tuple[List[ScrewPath], List[ScrewPath]]:
        """
        根据角度约束过滤路径组合
        
        Args:
            paths1: 螺钉1的候选路径
            paths2: 螺钉2的候选路径
            max_angle: 最大允许角度（度）
            
        Returns:
            过滤后的路径组合
        """
        max_angle_rad = math.radians(max_angle)
        filtered_paths1 = []
        filtered_paths2 = []
        
        for path1 in paths1:
            for path2 in paths2:
                # 计算两条路径的夹角
                dir1 = path1.get_direction_vector()
                dir2 = path2.get_direction_vector()
                
                dot_product = dir1.dot(dir2)
                # 限制dot_product在[-1, 1]范围内，避免数值误差
                dot_product = max(-1.0, min(1.0, dot_product))
                angle = math.acos(abs(dot_product))
                
                if angle <= max_angle_rad:
                    if path1 not in filtered_paths1:
                        filtered_paths1.append(path1)
                    if path2 not in filtered_paths2:
                        filtered_paths2.append(path2)
        
        logging.info(f"角度约束过滤后 - 螺钉1路径: {len(filtered_paths1)}, 螺钉2路径: {len(filtered_paths2)}")
        return filtered_paths1, filtered_paths2
    
    def calculate_prosthesis_parameters(self, reference_points: List[Point3D]) -> Tuple[Point3D, Vector3D, Vector3D]:
        """
        根据参考点计算假体参数
        
        Args:
            reference_points: 参考点列表 [p1, p2, p3, p4, p5, p6]
            
        Returns:
            (基座中心点, 法向量epsilon, 方向向量m)
        """
        if len(reference_points) < 4:
            raise ValueError("至少需要4个参考点")
        
        p1, p2, p3, p4 = reference_points[:4]
        
        # 计算假体平面的法向量
        v1 = p2 - p1
        v2 = p3 - p1
        epsilon = v1.cross(v2).normalize()
        
        # 计算方向向量m
        m = (p4 - p1).normalize()
        
        return p1, epsilon, m
